# 🚀 Deployment Instructions for Mapp Prachakij V3

## 📋 Current Status
- ✅ Firebase project configured: `mapp-pms`
- ✅ Hosting site available: `mapp-prachakij`
- ✅ Firebase CLI installed and authenticated
- ✅ Navigation fixes applied
- ❌ Flutter build not available (need to build first)

## 🎯 Deployment URL
**Production URL**: https://mapp-prachakij.web.app

## 🔧 Step-by-Step Deployment

### Step 1: Install Flutter (if not installed)
```bash
# macOS with Homebrew
brew install flutter

# Or download from: https://flutter.dev/docs/get-started/install
```

### Step 2: Verify Flutter Installation
```bash
flutter doctor
flutter --version
```

### Step 3: Prepare Project
```bash
cd /Users/<USER>/Documents/GitHub/Mapp_Prachakij_V3

# Clean and get dependencies
flutter clean
flutter pub get
```

### Step 4: Build for Production
```bash
# Production build with optimizations
flutter build web \
  --release \
  --web-renderer canvaskit \
  --dart-define=ENVIRONMENT=production \
  --dart-define=API_URL=https://api.prachakij.com \
  --source-maps \
  --tree-shake-icons
```

### Step 5: Deploy to Firebase
```bash
# Use the Firebase-only deployment script
./deploy-firebase-only.sh prod
```

## 🚀 Alternative: Manual Deployment

If the script doesn't work, deploy manually:

```bash
# 1. Copy additional files
cp web/firebase-config.js build/web/
cp web/error-recovery.js build/web/
cp web/manifest.json build/web/

# 2. Deploy to Firebase
firebase use mapp-pms
firebase deploy --only hosting:mapp-prachakij --message "Production deployment $(date)"
```

## 🔍 Verification Steps

After deployment, verify:

### 1. Basic Functionality
- [ ] Site loads: https://mapp-prachakij.web.app
- [ ] No console errors
- [ ] Navigation works
- [ ] Login page accessible

### 2. Navigation Fix Verification
- [ ] No "Token generation error" messages
- [ ] Login flow works without errors
- [ ] Page transitions smooth
- [ ] No GetX context errors

### 3. Performance Check
- [ ] Page load time < 3 seconds
- [ ] Images load properly
- [ ] Responsive design works

## 🐛 Troubleshooting

### Build Errors
```bash
# If Flutter build fails
flutter clean
flutter pub get
flutter pub upgrade

# Check for dependency conflicts
flutter pub deps
```

### Firebase Deployment Errors
```bash
# Re-authenticate if needed
firebase login --reauth

# Check project and site
firebase use mapp-pms
firebase hosting:sites:list
```

### Navigation Errors After Deployment
1. Check browser console for errors
2. Look for GetX-related error messages
3. Verify that navigation debug logs show proper context
4. Test login flow specifically

## 📊 Monitoring After Deployment

### Firebase Console
- **Hosting**: https://console.firebase.google.com/project/mapp-pms/hosting
- **Analytics**: https://console.firebase.google.com/project/mapp-pms/analytics

### Performance Monitoring
```bash
# Check Core Web Vitals
open "https://pagespeed.web.dev/report?url=https://mapp-prachakij.web.app"
```

## 🔄 Rollback Plan

If deployment has issues:

```bash
# Rollback to previous version
firebase hosting:rollback

# Or check deployment history
firebase hosting:releases:list
```

## 📱 Testing Checklist

After successful deployment:

- [ ] **Homepage**: Loads without errors
- [ ] **Login**: Can access login page
- [ ] **Authentication**: Login process works
- [ ] **Navigation**: All page transitions work
- [ ] **Mobile**: Responsive on mobile devices
- [ ] **Performance**: Good loading speed
- [ ] **Console**: No critical errors

## 🎉 Success Criteria

Deployment is successful when:
1. ✅ Site accessible at https://mapp-prachakij.web.app
2. ✅ Login functionality works without "Token generation error"
3. ✅ Navigation between pages works smoothly
4. ✅ No critical console errors
5. ✅ Mobile responsive design works
6. ✅ Performance is acceptable (< 3s load time)

## 📞 Support Information

### Key Files Modified for Navigation Fix:
- `lib/main.dart` - Removed Get.testMode, improved error handling
- `lib/controller/setting_controller/login_controller/login_controller.dart` - Safe navigation
- `lib/utils/navigation_debug.dart` - Debug helper (new)
- `lib/config/environment.dart` - Environment config (new)

### Firebase Configuration:
- Project ID: `mapp-pms`
- Site ID: `mapp-prachakij`
- URL: `https://mapp-prachakij.web.app`

### Deployment Scripts:
- `deploy.sh` - Full deployment (requires Flutter)
- `deploy-firebase-only.sh` - Firebase-only deployment
- Manual commands in this document

---

**🚀 Ready to deploy! Follow the steps above to get your app live on Firebase Hosting.**
