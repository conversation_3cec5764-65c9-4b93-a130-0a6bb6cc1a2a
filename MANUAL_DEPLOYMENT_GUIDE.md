# 📖 Manual Deployment Guide for Mapp Prachakij V3

## 🎯 Overview
คู่มือนี้สำหรับการ deploy แอป Flutter ขึ้น Firebase Hosting แบบ manual เมื่อไม่มี Flutter CLI ในระบบ

## 📋 Prerequisites

### 1. ติดตั้ง Flutter (ถ้ายังไม่มี)
```bash
# macOS
brew install flutter

# หรือ download จาก https://flutter.dev/docs/get-started/install
```

### 2. ตรวจสอบ Firebase CLI
```bash
firebase --version
# ถ้าไม่มี: npm install -g firebase-tools
```

### 3. Login Firebase
```bash
firebase login
```

## 🚀 Step-by-Step Deployment

### Step 1: เตรียม Environment
```bash
cd /Users/<USER>/Documents/GitHub/Mapp_Prachakij_V3

# ตรวจสอบ Flutter
flutter doctor

# Clean project
flutter clean
flutter pub get
```

### Step 2: Build สำหรับ Production
```bash
# Production Build
flutter build web \
  --release \
  --web-renderer canvaskit \
  --dart-define=ENVIRONMENT=production \
  --dart-define=API_URL=https://api.prachakij.com \
  --source-maps \
  --tree-shake-icons
```

### Step 3: ตรวจสอบ Build Output
```bash
# ตรวจสอบว่า build/web มีไฟล์ครบ
ls -la build/web/

# ควรมีไฟล์เหล่านี้:
# - index.html
# - main.dart.js
# - flutter.js
# - assets/
# - icons/
```

### Step 4: Copy Additional Files
```bash
# Copy Firebase config
cp web/firebase-config.js build/web/

# Copy error recovery script (ถ้ามี)
cp web/error-recovery.js build/web/

# Copy other static files
cp web/manifest.json build/web/
```

### Step 5: ตั้งค่า Firebase Project
```bash
# ตรวจสอบ project
firebase projects:list

# เลือก project
firebase use mapp-pms

# ตรวจสอบการตั้งค่า
cat .firebaserc
cat firebase.json
```

### Step 6: Deploy to Firebase Hosting
```bash
# Deploy
firebase deploy --only hosting:mapp-prachakij

# หรือ deploy พร้อม message
firebase deploy --only hosting:mapp-prachakij --message "Production deployment $(date)"
```

### Step 7: ตรวจสอบ Deployment
```bash
# เปิด URL ที่ deploy แล้ว
open https://mapp-prachakij.web.app

# ตรวจสอบ Firebase console
open https://console.firebase.google.com/project/mapp-pms/hosting
```

## 🔧 Alternative Build Commands

### Staging Build
```bash
flutter build web \
  --release \
  --web-renderer canvaskit \
  --dart-define=ENVIRONMENT=staging \
  --dart-define=API_URL=https://staging-api.prachakij.com \
  --source-maps
```

### Development Build
```bash
flutter build web \
  --web-renderer canvaskit \
  --dart-define=ENVIRONMENT=development \
  --dart-define=API_URL=https://dev-api.prachakij.com
```

## 🐛 Troubleshooting

### Build Errors
```bash
# ถ้า build ไม่สำเร็จ
flutter clean
flutter pub get
flutter pub upgrade

# ลองใหม่
flutter build web --release
```

### Firebase Deployment Errors
```bash
# ตรวจสอบ authentication
firebase login --reauth

# ตรวจสอบ project
firebase use --add

# ตรวจสอบ hosting configuration
firebase hosting:sites:list
```

### Navigation Errors
```bash
# ตรวจสอบ console logs ใน browser
# เปิด Developer Tools > Console

# ตรวจสอบ GetX setup
# ดู logs ที่ขึ้นต้นด้วย 🔍 Navigation Debug
```

## 📱 Testing After Deployment

### 1. Basic Functionality
- [ ] หน้าแรกโหลดได้
- [ ] Navigation ระหว่างหน้าทำงาน
- [ ] Login/logout ทำงาน
- [ ] ไม่มี console errors

### 2. Performance
- [ ] Page load speed < 3 seconds
- [ ] Images โหลดเร็ว
- [ ] Smooth animations

### 3. Mobile Testing
- [ ] Responsive design
- [ ] Touch interactions
- [ ] Mobile browsers

## 🔄 Rollback Instructions

### ถ้า deployment มีปัญหา:
```bash
# Rollback to previous version
firebase hosting:rollback

# หรือ deploy version เก่า
firebase deploy --only hosting:mapp-prachakij
```

## 📊 Monitoring

### Firebase Console
- **Hosting**: https://console.firebase.google.com/project/mapp-pms/hosting
- **Analytics**: https://console.firebase.google.com/project/mapp-pms/analytics
- **Crashlytics**: https://console.firebase.google.com/project/mapp-pms/crashlytics

### Performance Monitoring
```bash
# ตรวจสอบ Core Web Vitals
# ใช้ Google PageSpeed Insights
open "https://pagespeed.web.dev/report?url=https://mapp-prachakij.web.app"
```

## 🎉 Success Checklist

เมื่อ deployment สำเร็จ:
- [ ] URL เข้าถึงได้: https://mapp-prachakij.web.app
- [ ] Login ทำงานปกติ
- [ ] Navigation ไม่มี error
- [ ] Performance ดี
- [ ] Mobile responsive
- [ ] Console ไม่มี critical errors

## 📞 Support

หากมีปัญหา:
1. ตรวจสอบ console logs
2. ดู Firebase console
3. ทดสอบ local build
4. ติดต่อทีมพัฒนา

---

**🚀 Ready to deploy manually!**
