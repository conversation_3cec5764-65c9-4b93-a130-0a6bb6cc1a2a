# 🚀 Mapp Prachakij V3 - Production Deployment Guide

## 📋 Prerequisites

### Required Tools
- **Flutter SDK** (latest stable version)
- **Firebase CLI** (`npm install -g firebase-tools`)
- **Git** (for version tracking)
- **gzip** (for asset compression - usually pre-installed)

### Firebase Setup
1. Login to Firebase: `firebase login`
2. Verify project access: `firebase projects:list`
3. Ensure you have deploy permissions for `mapp-pms` project

## 🛠 Deployment Script Usage

### Basic Commands

```bash
# Production deployment (default)
./deploy.sh

# Staging deployment
./deploy.sh staging

# Development deployment
./deploy.sh dev

# Production with HTML renderer (smaller bundle)
./deploy.sh prod html

# Staging with CanvasKit renderer (better performance)
./deploy.sh staging canvaskit
```

### Script Parameters
- **Environment**: `prod` (default), `staging`, `dev`
- **Renderer**: `canvaskit` (default), `html`

## 🔧 What the Script Does

### 1. Pre-deployment Checks
- ✅ Verifies Flutter installation
- ✅ Verifies Firebase CLI installation
- ✅ Checks Firebase authentication

### 2. Build Process
- 🧹 Cleans previous builds
- 📦 Updates dependencies
- 🔨 Builds Flutter web app with optimizations
- 📋 Copies additional files
- ⚡ Compresses assets (production only)

### 3. Production Optimizations
- **Tree-shaking**: Removes unused code
- **Source maps**: For debugging
- **PWA strategy**: Offline-first caching
- **Asset compression**: Gzip compression
- **Version tracking**: Automatic versioning

### 4. Deployment
- 🔐 Verifies Firebase authentication
- 🎯 Sets correct Firebase project
- 🚀 Deploys to Firebase Hosting
- 📊 Logs deployment information

## 📊 Build Configurations

### Production (`prod`)
```bash
flutter build web \
    --release \
    --web-renderer canvaskit \
    --dart-define=ENVIRONMENT=production \
    --dart-define=API_URL=https://api.prachakij.com \
    --source-maps \
    --tree-shake-icons \
    --pwa-strategy=offline-first
```

### Staging (`staging`)
```bash
flutter build web \
    --release \
    --web-renderer canvaskit \
    --dart-define=ENVIRONMENT=staging \
    --dart-define=API_URL=https://staging-api.prachakij.com \
    --source-maps \
    --pwa-strategy=offline-first
```

### Development (`dev`)
```bash
flutter build web \
    --web-renderer canvaskit \
    --dart-define=ENVIRONMENT=development \
    --dart-define=API_URL=https://dev-api.prachakij.com
```

## 🔒 Security Features

### Production Deployment
- **Confirmation prompt**: Requires manual confirmation
- **Version tracking**: Automatic version and build number
- **Deployment logging**: All deployments are logged
- **Asset optimization**: Compressed and cached assets

### Environment Variables
- `ENVIRONMENT`: Current deployment environment
- `API_URL`: API endpoint for the environment
- `VERSION`: Timestamp-based version
- `BUILD_NUMBER`: Git commit hash

## 📁 Generated Files

### Version Information
- `build/web/version.json`: Contains deployment metadata
- `deployment.log`: Deployment history log

### Compressed Assets (Production)
- `*.js.gz`: Compressed JavaScript files
- `*.css.gz`: Compressed CSS files
- `*.html.gz`: Compressed HTML files

## 🌐 Firebase Hosting Configuration

### Caching Strategy
- **Static assets**: 1 year cache (immutable)
- **HTML files**: No cache (always fresh)
- **JSON files**: 5 minutes cache
- **Version file**: No cache

### Compression
- **Gzip encoding**: Enabled for all text-based files
- **Content-Type**: Properly set for all file types

## 🚨 Troubleshooting

### Common Issues

#### 1. Flutter not found
```bash
# Add Flutter to PATH or use full path
export PATH="$PATH:/path/to/flutter/bin"
```

#### 2. Firebase authentication failed
```bash
firebase login
firebase projects:list
```

#### 3. Build failed
```bash
flutter clean
flutter pub get
flutter doctor
```

#### 4. Deployment permission denied
- Verify Firebase project permissions
- Check if you're logged in with correct account

### Build Optimization Issues

#### Large bundle size
- Use HTML renderer: `./deploy.sh prod html`
- Check for unused dependencies
- Optimize images and assets

#### Slow loading
- Use CanvasKit renderer: `./deploy.sh prod canvaskit`
- Enable PWA caching
- Optimize API calls

## 📈 Monitoring

### Deployment Logs
```bash
# View deployment history
cat deployment.log

# Monitor latest deployments
tail -f deployment.log
```

### Version Checking
```bash
# Check deployed version
curl https://mapp-prachakij.web.app/version.json
```

## 🔄 Rollback Strategy

### Quick Rollback
1. Go to Firebase Console
2. Navigate to Hosting
3. Select previous deployment
4. Click "Rollback"

### Script-based Rollback
```bash
# Deploy previous version
git checkout <previous-commit>
./deploy.sh prod
git checkout main
```

## 📞 Support

### Deployment Issues
- Check Firebase Console for hosting status
- Review deployment logs
- Verify build output in `build/web`

### Performance Issues
- Use Firebase Performance Monitoring
- Check browser developer tools
- Monitor API response times

---

## 🎯 Quick Start

1. **Make script executable**:
   ```bash
   chmod +x deploy.sh
   ```

2. **Deploy to production**:
   ```bash
   ./deploy.sh
   ```

3. **Verify deployment**:
   - Visit: https://mapp-prachakij.web.app
   - Check version: https://mapp-prachakij.web.app/version.json

---

**⚠️ Important**: Always test in staging before deploying to production!
