#!/bin/bash

# 🚀 Firebase-Only Deployment Script for Mapp Prachakij V3
# สำหรับกรณีที่มี build/web อยู่แล้วและต้องการ deploy เฉพาะ Firebase

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
ENVIRONMENT=${1:-prod}
PROJECT_ID="mapp-pms"
SITE_ID="mapp-prachakij"

echo -e "${BLUE}🚀 Firebase-Only Deployment for environment: ${ENVIRONMENT}${NC}"

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if Firebase CLI is installed
if ! command -v firebase &> /dev/null; then
    print_error "Firebase CLI is not installed"
    echo "Install with: npm install -g firebase-tools"
    exit 1
fi

print_status "Firebase CLI found"

# Check if build/web exists
if [ ! -d "build/web" ]; then
    print_error "build/web directory not found"
    echo "Please run Flutter build first:"
    echo "flutter build web --release --web-renderer canvaskit"
    exit 1
fi

print_status "Build directory found"

# Copy additional files if needed
echo -e "${BLUE}📋 Copying additional files...${NC}"

# Ensure firebase-config.js is in build
if [ -f "web/firebase-config.js" ]; then
    cp web/firebase-config.js build/web/
    print_status "Firebase config copied"
fi

# Copy error recovery script
if [ -f "web/error-recovery.js" ]; then
    cp web/error-recovery.js build/web/
    print_status "Error recovery script copied"
fi

# Copy manifest.json
if [ -f "web/manifest.json" ]; then
    cp web/manifest.json build/web/
    print_status "Manifest copied"
fi

# Firebase login check
echo -e "${BLUE}🔐 Checking Firebase authentication...${NC}"
if ! firebase projects:list &> /dev/null; then
    print_warning "Not logged in to Firebase"
    echo "Please run: firebase login"
    firebase login
fi

print_status "Firebase authentication verified"

# Set Firebase project
echo -e "${BLUE}🎯 Setting Firebase project...${NC}"
firebase use $PROJECT_ID
print_status "Firebase project set to: $PROJECT_ID"

# Show current hosting sites
echo -e "${BLUE}🌐 Available hosting sites:${NC}"
firebase hosting:sites:list

# Deploy to Firebase Hosting
echo -e "${BLUE}🚀 Deploying to Firebase Hosting...${NC}"

if [ "$ENVIRONMENT" = "prod" ]; then
    # Production deployment
    firebase deploy --only hosting:$SITE_ID --message "Production deployment $(date)"
    DEPLOY_URL="https://$SITE_ID.web.app"
elif [ "$ENVIRONMENT" = "staging" ]; then
    # Staging deployment
    firebase deploy --only hosting:$SITE_ID --message "Staging deployment $(date)"
    DEPLOY_URL="https://$SITE_ID.web.app"
else
    # Development deployment
    firebase deploy --only hosting:$SITE_ID --message "Development deployment $(date)"
    DEPLOY_URL="https://$SITE_ID.web.app"
fi

print_status "Deployment completed successfully!"

# Display deployment information
echo -e "${GREEN}"
echo "=========================================="
echo "🎉 DEPLOYMENT SUCCESSFUL!"
echo "=========================================="
echo "Environment: $ENVIRONMENT"
echo "Project ID: $PROJECT_ID"
echo "Site ID: $SITE_ID"
echo "URL: $DEPLOY_URL"
echo "Deployed at: $(date)"
echo "=========================================="
echo -e "${NC}"

# Show deployment info
echo -e "${BLUE}📊 Deployment Information:${NC}"
firebase hosting:sites:get $SITE_ID

# Open browser (optional)
read -p "Open deployed site in browser? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    if command -v open &> /dev/null; then
        open $DEPLOY_URL
    elif command -v xdg-open &> /dev/null; then
        xdg-open $DEPLOY_URL
    else
        echo "Please open: $DEPLOY_URL"
    fi
fi

print_status "Firebase deployment completed!"

# Show next steps
echo -e "${BLUE}📋 Next Steps:${NC}"
echo "1. Test the deployed application: $DEPLOY_URL"
echo "2. Check Firebase console: https://console.firebase.google.com/project/$PROJECT_ID/hosting"
echo "3. Monitor for any errors in the console"
echo "4. Test login functionality"
echo "5. Verify all pages load correctly"

echo -e "${GREEN}🎯 Deployment URL: $DEPLOY_URL${NC}"
