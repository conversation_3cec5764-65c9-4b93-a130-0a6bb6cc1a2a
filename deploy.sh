#!/bin/bash

# 🚀 Firebase Hosting Deployment Script for Mapp Prachakij V3
# Usage: ./deploy.sh [environment]
# Environment: dev, staging, prod (default: prod)

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
ENVIRONMENT=${1:-prod}
PROJECT_ID="mapp-pms"
SITE_ID="mapp-prachakij"

echo -e "${BLUE}🚀 Starting deployment for environment: ${ENVIRONMENT}${NC}"

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if Flutter is installed
if ! command -v flutter &> /dev/null; then
    print_error "Flutter is not installed or not in PATH"
    echo "Please install Flutter: https://flutter.dev/docs/get-started/install"
    exit 1
fi

# Check if Firebase CLI is installed
if ! command -v firebase &> /dev/null; then
    print_error "Firebase CLI is not installed"
    echo "Install with: npm install -g firebase-tools"
    exit 1
fi

print_status "Environment checks passed"

# Clean previous builds
echo -e "${BLUE}🧹 Cleaning previous builds...${NC}"
flutter clean
rm -rf build/web
print_status "Clean completed"

# Get dependencies
echo -e "${BLUE}📦 Getting dependencies...${NC}"
flutter pub get
print_status "Dependencies updated"

# Run tests (optional - comment out if no tests)
# echo -e "${BLUE}🧪 Running tests...${NC}"
# flutter test
# print_status "Tests passed"

# Build for web with optimizations
echo -e "${BLUE}🔨 Building Flutter web app...${NC}"

if [ "$ENVIRONMENT" = "prod" ]; then
    print_status "Building for PRODUCTION"
    flutter build web \
        --release \
        --web-renderer canvaskit \
        --dart-define=ENVIRONMENT=production \
        --dart-define=API_URL=https://api.prachakij.com \
        --source-maps \
        --tree-shake-icons
elif [ "$ENVIRONMENT" = "staging" ]; then
    print_status "Building for STAGING"
    flutter build web \
        --release \
        --web-renderer canvaskit \
        --dart-define=ENVIRONMENT=staging \
        --dart-define=API_URL=https://staging-api.prachakij.com \
        --source-maps
else
    print_status "Building for DEVELOPMENT"
    flutter build web \
        --web-renderer canvaskit \
        --dart-define=ENVIRONMENT=development \
        --dart-define=API_URL=https://dev-api.prachakij.com
fi

print_status "Flutter build completed"

# Verify build output
if [ ! -d "build/web" ]; then
    print_error "Build failed - build/web directory not found"
    exit 1
fi

print_status "Build verification passed"

# Copy additional files if needed
echo -e "${BLUE}📋 Copying additional files...${NC}"

# Ensure firebase-config.js is in build
if [ -f "web/firebase-config.js" ]; then
    cp web/firebase-config.js build/web/
    print_status "Firebase config copied"
fi

# Copy error recovery script
if [ -f "web/error-recovery.js" ]; then
    cp web/error-recovery.js build/web/
    print_status "Error recovery script copied"
fi

# Firebase login check
echo -e "${BLUE}🔐 Checking Firebase authentication...${NC}"
if ! firebase projects:list &> /dev/null; then
    print_warning "Not logged in to Firebase"
    echo "Please run: firebase login"
    firebase login
fi

print_status "Firebase authentication verified"

# Set Firebase project
echo -e "${BLUE}🎯 Setting Firebase project...${NC}"
firebase use $PROJECT_ID
print_status "Firebase project set to: $PROJECT_ID"

# Deploy to Firebase Hosting
echo -e "${BLUE}🚀 Deploying to Firebase Hosting...${NC}"

if [ "$ENVIRONMENT" = "prod" ]; then
    # Production deployment
    firebase deploy --only hosting:$SITE_ID --message "Production deployment $(date)"
    DEPLOY_URL="https://$SITE_ID.web.app"
elif [ "$ENVIRONMENT" = "staging" ]; then
    # Staging deployment (if you have staging site)
    firebase deploy --only hosting:$SITE_ID --message "Staging deployment $(date)"
    DEPLOY_URL="https://$SITE_ID.web.app"
else
    # Development deployment
    firebase deploy --only hosting:$SITE_ID --message "Development deployment $(date)"
    DEPLOY_URL="https://$SITE_ID.web.app"
fi

print_status "Deployment completed successfully!"

# Display deployment information
echo -e "${GREEN}"
echo "=========================================="
echo "🎉 DEPLOYMENT SUCCESSFUL!"
echo "=========================================="
echo "Environment: $ENVIRONMENT"
echo "Project ID: $PROJECT_ID"
echo "Site ID: $SITE_ID"
echo "URL: $DEPLOY_URL"
echo "Deployed at: $(date)"
echo "=========================================="
echo -e "${NC}"

# Open browser (optional)
read -p "Open deployed site in browser? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    if command -v open &> /dev/null; then
        open $DEPLOY_URL
    elif command -v xdg-open &> /dev/null; then
        xdg-open $DEPLOY_URL
    else
        echo "Please open: $DEPLOY_URL"
    fi
fi

print_status "Deployment script completed!"
