{"hosting": {"site": "mapp-prachakij", "public": "build/web", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}], "headers": [{"source": "**/*.js", "headers": [{"key": "Content-Type", "value": "application/javascript; charset=utf-8"}, {"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "**/*.dart.js", "headers": [{"key": "Content-Type", "value": "application/javascript; charset=utf-8"}, {"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "**/*.css", "headers": [{"key": "Content-Type", "value": "text/css; charset=utf-8"}, {"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "**/*.html", "headers": [{"key": "Content-Type", "value": "text/html; charset=utf-8"}, {"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}]}, {"source": "**/*.woff2", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "**/*.png", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "**/*.jpg", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "**/*.ico", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}]}}