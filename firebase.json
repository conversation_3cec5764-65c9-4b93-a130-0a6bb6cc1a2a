{"hosting": {"site": "mapp-prachakij", "public": "build/web", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}], "headers": [{"source": "**/*.js", "headers": [{"key": "Content-Type", "value": "application/javascript; charset=utf-8"}, {"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}]}, {"source": "**/*.dart.js", "headers": [{"key": "Content-Type", "value": "application/javascript; charset=utf-8"}, {"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}]}, {"source": "**/*.css", "headers": [{"key": "Content-Type", "value": "text/css; charset=utf-8"}, {"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}]}, {"source": "**/*.html", "headers": [{"key": "Content-Type", "value": "text/html; charset=utf-8"}, {"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}]}]}}