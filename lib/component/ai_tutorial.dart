import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';

import '../controller/ai_tutorial_controller.dart';

class AiTutorial extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    // ✅ ตรวจสอบว่า AiTutorialController  register แล้ว
    if (!Get.isRegistered<AiTutorialController>()) {
      Get.put(AiTutorialController(), permanent: true);
    }

    final aiTutorialCtl = Get.find<AiTutorialController>();

    return Obx(() {
      if (aiTutorialCtl.currentBubbleIndex.value == -1) {
        return const SizedBox.shrink();
      }

      final index = aiTutorialCtl.currentBubbleIndex.value;
      if (index >= aiTutorialCtl.bubbleDataList.length) {
        return const SizedBox.shrink();
      }

      final data = aiTutorialCtl.bubbleDataList[index];

      return Positioned(
        top: data["top"] ?? 100.0,
        right: (MediaQuery.of(context).size.width - (data["width"] ?? 290)) / 2,
        left: (MediaQuery.of(context).size.width - (data["width"] ?? 290)) / 2,
        child: SpeechBubbleWithArrow(
            arrowOffsetLeft: data["offset"] ?? 20,
            width: Get.width,
            arrowPosition: data["arrowPosition"] ?? "top",
            borderColor: const Color(0xFFFF9701),
            backgroundColor: Colors.white,
            child: Text(
              data["text"],
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Color(0xFF282828),
                shadows: [
                  Shadow(
                    color: Colors.black.withOpacity(0.2),
                    offset: Offset(0, 1),
                    blurRadius: 2,
                  ),
                ],
              ),
            )),
      );
    });
  }
}

class SpeechBubbleWithArrow extends StatelessWidget {
  final Widget child;
  final Color backgroundColor;
  final Color borderColor;
  final double arrowOffsetLeft;
  final double width;
  final String arrowPosition;
  final double? top;
  final double? left;
  final double? right;
  final double? height;

  const SpeechBubbleWithArrow({
    Key? key,
    required this.child,
    this.backgroundColor = Colors.white,
    this.borderColor = Colors.red,
    this.arrowOffsetLeft = 23,
    this.width = 290.0,
    this.arrowPosition = "top",
    this.top,
    this.left,
    this.right,
    this.height = 65,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final arrow = Positioned(
      top: arrowPosition == "top" ? 0 : null,
      bottom: arrowPosition == "bottom" ? 0 : null,
      left: arrowOffsetLeft,
      child: CustomPaint(
        size: const Size(30, 15),
        painter: TrianglePainter(
          isDown: arrowPosition == "bottom",
          color: backgroundColor,
          borderColor: borderColor,
        ),
      ),
    );

    return Stack(
      clipBehavior: Clip.none,
      children: [
        arrow,
        Container(
          margin: EdgeInsets.only(
              top: arrowPosition == "top" ? 8 : 0,
              bottom: arrowPosition == "bottom" ? 8 : 0),
          width: width,
          height: height,
          decoration: BoxDecoration(
            color: backgroundColor,
            border: Border.all(color: borderColor, width: 0.5),
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.15),
                blurRadius: 8,
                offset: const Offset(1, 1),
              ),
            ],
          ),
          child: Row(
            children: [
              Container(
                height: 57,
                width: 57,
                margin: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: const Color(0xFFF2F3F6),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Lottie.network(
                  'https://cdn.lottielab.com/l/C7yruvhC2E1zZm.json',
                  fit: BoxFit.contain,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(child: child),
              const SizedBox(width: 8),
              GestureDetector(
                onTap: () {
                  // อย่าง:  ให้ทำงานตามเงื่อนไข
                  final ctl = Get.find<AiTutorialController>();

                  // เงื่อนไข ถ้า บางอย่าง ต้องการกำหนดก่อน  ให้ทำงานตามเงื่อนไข
                  if (ctl.currentBubbleIndex.value != -1) {
                    ctl.saveShowAiTutorialDate();
                    ctl.currentBubbleIndex.value = -1; // ซ่อน bubble
                  }
                },
                child: Padding(
                  padding: EdgeInsets.all(4.0),
                  child: Align(
                      alignment: Alignment.topRight,
                      child: const Icon(Icons.close,
                          size: 14, color: Colors.black54)),
                ),
              ),
              const SizedBox(width: 8),
            ],
          ),
        ),
      ],
    );
  }
}

class TrianglePainter extends CustomPainter {
  final bool isDown;
  final Color color;
  final Color borderColor;

  TrianglePainter(
      {this.isDown = false,
      this.color = Colors.white,
      this.borderColor = const Color(0xFFFF9701)});

  @override
  void paint(Canvas canvas, Size size) {
    final path = Path();
    if (isDown) {
      path.moveTo(size.width / 2, size.height);
      path.lineTo(0, 0);
      path.lineTo(size.width, 0);
    } else {
      path.moveTo(size.width / 2, 0);
      path.lineTo(0, size.height);
      path.lineTo(size.width, size.height);
    }
    path.close();

    final fillPaint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final borderPaint = Paint()
      ..color = borderColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.5;

    canvas.drawPath(path, fillPaint);
    canvas.drawPath(path, borderPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
