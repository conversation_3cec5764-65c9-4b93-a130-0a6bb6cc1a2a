import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/controller/psiController/psi_controller.dart';
import 'package:mapp_prachakij_v3/controller/service/service.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:mapp_prachakij_v3/controller/tutorial_controller.dart';
import 'package:mapp_prachakij_v3/view/mobile/home_mobile.dart';

import '../view/home/<USER>';

class AppAlert {
  static showAlertDel(context, title, message, submessage, btnText) {
    return showDialog(
        barrierColor: Colors.transparent,
        barrierDismissible: true,
        context: context,
        builder: (BuildContext context) {
          return Center(
            child: AlertDialog(
              backgroundColor: Colors.white.withOpacity(0.98),
              titlePadding: const EdgeInsets.fromLTRB(25, 30, 25, 0),
              contentPadding: const EdgeInsets.fromLTRB(25, 12, 25, 0),
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.all(
                  Radius.circular(20),
                ),
              ),
              title: Image.asset(
                'assets/icon/contact_icon.png',
                height: 43.74,
                width: 50,
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    title,
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      fontFamily: 'Prompt-Medium',
                      fontSize: 14,
                      color: Color(0xFF000000),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  Text(
                    message,
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      fontFamily: 'Prompt',
                      fontSize: 11,
                      color: Color(0xFF4B4B4B),
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  Text(
                    submessage,
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      fontFamily: 'Prompt',
                      fontSize: 14,
                      color: Color(0xFF282828),
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      InkWell(
                        onTap: () {
                          AppService.launchUrl(
                            'https://m.me/prachakij',
                          );
                        },
                        child: Image.asset(
                          'assets/image/drawer/contactus/IconblackMessenger.png',
                          width: 30,
                        ),
                      ),
                      const SizedBox(
                        width: 20,
                      ),
                      InkWell(
                        onTap: () {
                          AppService.launchUrl('fb://profile/145205572173862');
                        },
                        child: Image.asset(
                          'assets/image/drawer/contactus/IconblackFacebook.png',
                          width: 35,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(
                    height: 0.02.sh,
                  ),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      InkWell(
                        onTap: () {
                          Navigator.pop(context);
                        },
                        child: Container(
                          alignment: Alignment.center,
                          width: 0.25.sw,
                          height: 0.04.sh,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.all(
                              Radius.circular(1.sw),
                            ),
                            border: Border.all(
                              width: 2,
                            ),
                            color: const Color(0xFFFFB100),
                          ),
                          child: Text(
                            btnText,
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontFamily: 'Prompt-Medium',
                              fontSize: 16.sp,
                              color: const Color(0xFFFFFFFF),
                              fontWeight: FontWeight.w500,
                              letterSpacing: 0.2,
                              shadows: <Shadow>[
                                Shadow(
                                  offset: const Offset(0, 1),
                                  blurRadius: 1.0,
                                  color:
                                  const Color(0xFF000000).withOpacity(0.15),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(
                    height: 0.04.sh,
                  ),
                ],
              ),
            ),
          );
        });
  }

  static showConfirm(context, title, message, btnText) {
    return showDialog(
        barrierColor: Colors.transparent,
        barrierDismissible: true,
        context: context,
        builder: (BuildContext context) {
          return Center(
            child: AlertDialog(
              backgroundColor: Colors.white.withOpacity(0.95),
              titlePadding: const EdgeInsets.fromLTRB(25, 30, 25, 0),
              contentPadding: const EdgeInsets.fromLTRB(25, 12, 25, 0),
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.all(
                  Radius.circular(20),
                ),
              ),
              title: Image.asset(
                'assets/icon/contact_icon.png',
                height: 43.74,
                width: 50,
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    title,
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      fontFamily: 'Prompt-Medium',
                      fontSize: 14,
                      color: Color(0xFF000000),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  Text(
                    message,
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      fontFamily: 'Prompt',
                      fontSize: 11,
                      color: Color(0xFF282828),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(
                    height: 20,
                  ),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      InkWell(
                        onTap: () {
                          Navigator.of(context).pop(true);
                        },
                        child: Container(
                          alignment: Alignment.center,
                          width: 132,
                          height: 40,
                          decoration: BoxDecoration(
                            borderRadius: const BorderRadius.all(
                              Radius.circular(58),
                            ),
                            border: Border.all(
                              width: 2,
                            ),
                            color: const Color(0xFFFFB100),
                          ),
                          child: Text(
                            btnText,
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontFamily: 'Prompt-Medium',
                              fontSize: 13,
                              color: const Color(0xFFFFFFFF),
                              fontWeight: FontWeight.w500,
                              shadows: <Shadow>[
                                Shadow(
                                  offset: const Offset(0, 1),
                                  blurRadius: 1.0,
                                  color:
                                  const Color(0xFF000000).withOpacity(0.15),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(
                    height: 5,
                  ),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      InkWell(
                        onTap: () {
                          Navigator.of(context).pop(false);
                        },
                        child: Container(
                          alignment: Alignment.center,
                          width: 132,
                          height: 40,
                          decoration: BoxDecoration(
                            borderRadius: const BorderRadius.all(
                              Radius.circular(58),
                            ),
                            border: Border.all(
                              width: 2,
                            ),
                            color: const Color(0xFF282828),
                          ),
                          child: Text(
                            "ยกเลิก",
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontFamily: 'Prompt-Medium',
                              fontSize: 13,
                              color: const Color(0xFFFFFFFF),
                              fontWeight: FontWeight.w500,
                              shadows: <Shadow>[
                                Shadow(
                                  offset: const Offset(0, 1),
                                  blurRadius: 1.0,
                                  color:
                                  const Color(0xFF000000).withOpacity(0.15),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(
                    height: 30,
                  ),
                ],
              ),
            ),
          );
        });
  }

  static showError(context, message, btnText) {
    return showDialog(
        barrierDismissible: true,
        context: context,
        builder: (BuildContext context) {
          return Center(
            child: AlertDialog(
              backgroundColor: Colors.white.withOpacity(0.95),
              titlePadding: const EdgeInsets.fromLTRB(25, 30, 25, 0),
              contentPadding: const EdgeInsets.fromLTRB(25, 12, 25, 0),
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.all(
                  Radius.circular(20),
                ),
              ),
              title: Image.asset(
                'assets/icon/contact_icon.png',
                height: 43.74,
                width: 50,
              ),
              content: Container(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      message,
                      textAlign: TextAlign.center,
                      style: const TextStyle(
                        fontFamily: 'Prompt',
                        fontSize: 14,
                        color: Color(0xFF000000),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        InkWell(
                          onTap: () {
                            Navigator.pop(context);
                          },
                          child: Container(
                            alignment: Alignment.center,
                            width: 132,
                            height: 40,
                            decoration: BoxDecoration(
                              borderRadius: const BorderRadius.all(
                                Radius.circular(58),
                              ),
                              border: Border.all(
                                width: 2,
                              ),
                              color: const Color(0xFFFFB100),
                            ),
                            child: Text(
                              btnText,
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                fontFamily: 'Prompt-Medium',
                                fontSize: 13,
                                color: const Color(0xFFFFFFFF),
                                fontWeight: FontWeight.w500,
                                shadows: <Shadow>[
                                  Shadow(
                                    offset: const Offset(0, 1),
                                    blurRadius: 1.0,
                                    color: const Color(0xFF000000)
                                        .withOpacity(0.15),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(
                      height: 30,
                    ),
                  ],
                ),
              ),
            ),
          );
        });
  }

  static showImageClaim(context, url) {
    return showDialog(
      barrierDismissible: true,
      context: context,
      builder: (BuildContext context) {
        return Center(
          child: AlertDialog(
            backgroundColor: Colors.white.withOpacity(0.95),
            contentPadding: EdgeInsets.zero,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.all(
                Radius.circular(
                  0.05.sh,
                ),
              ),
              side: const BorderSide(
                width: 1.5,
                color: Colors.black,
              ),
            ),
            title: Column(
              children: <Widget>[
                Container(
                  margin: EdgeInsets.only(
                    top: 0.02.sh,
                    bottom: 0.02.sh,
                  ),
                  child: Text(
                    'รูปภาพที่อัพโหลด',
                    style: TextStyle(
                      fontFamily: 'Prompt',
                      color: const Color(0xFF282828),
                      fontSize: 20.sp,
                      letterSpacing: 0.4,
                      shadows: <Shadow>[
                        Shadow(
                          offset: const Offset(0, 1),
                          blurRadius: 1.0,
                          color: const Color(0xFF000000).withOpacity(0.15),
                        ),
                      ],
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                CachedNetworkImage(
                  imageUrl: url,
                  imageBuilder: (context, imageProvider) => Container(
                    width: 0.8.sw,
                    height: 0.4.sh,
                    decoration: BoxDecoration(
                      image: DecorationImage(
                        image: imageProvider,
                        // fit: BoxFit.fitWidth,
                      ),
                    ),
                  ),
                  fit: BoxFit.cover,
                  placeholder: (context, url) => SizedBox(
                    width: 0.2.sh,
                    height: 0.2.sh,
                    child: const Center(
                      child: CircularProgressIndicator(
                        color: Colors.orange,
                      ),
                    ),
                  ),
                  errorWidget: (context, url, error) => Container(
                    width: 0.2.sh,
                    height: 0.2.sh,
                    decoration: BoxDecoration(
                      boxShadow: [
                        BoxShadow(
                          color: const Color(0xFF000000).withOpacity(0.1),
                          offset: const Offset(0, 3),
                          blurRadius: 3.0,
                        ),
                      ],
                    ),
                    child: const Center(
                      child: Icon(
                        Icons.error,
                        color: Color(0xFFFFB100),
                      ),
                    ),
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(
                    top: 0.02.sh,
                    bottom: 0.02.sh,
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: <Widget>[
                      GestureDetector(
                        onTap: () {
                          Navigator.of(context).pop(true);
                        },
                        child: SizedBox(
                          width: 0.3.sw,
                          height: 0.05.sh,
                          child: Container(
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.all(
                                  Radius.circular(0.08.sw),
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: const Color(0xFF000000)
                                        .withOpacity(0.1),
                                    blurRadius: 5,
                                    offset: const Offset(0, 2),
                                  )
                                ],
                                color: const Color(0xFF333333)),
                            child: Text(
                              "ปิดหน้านี้",
                              style: TextStyle(
                                fontFamily: "Prompt-Medium",
                                fontSize: 16.sp,
                                letterSpacing: 0.4,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),
                      )
                    ],
                  ),
                )
              ],
            ),
          ),
        );
      },
    );
  }

  static toastError(text) {
    Fluttertoast.showToast(
      msg: text,
      toastLength: Toast.LENGTH_SHORT,
      gravity: ToastGravity.TOP,
      timeInSecForIosWeb: 2,
      backgroundColor: Colors.red,
      textColor: Colors.white,
      fontSize: 16.0,
    );
  }

  static showNewConfirm(context, title, message, btnText1, btnText2) {
    return showDialog(
        barrierColor: Colors.transparent,
        barrierDismissible: false,
        context: context,
        builder: (BuildContext context) {
          return Center(
            child: AlertDialog(
              backgroundColor: Colors.white.withOpacity(0.98),
              titlePadding: const EdgeInsets.fromLTRB(25, 30, 25, 0),
              contentPadding: const EdgeInsets.fromLTRB(25, 12, 25, 0),
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.all(
                  Radius.circular(20),
                ),
              ),
              title: Image.asset(
                'assets/icon/contact_icon.png',
                height: 43.74,
                width: 50,
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    title,
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      fontFamily: 'Prompt-Medium',
                      fontSize: 14,
                      color: Color(0xFF000000),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  Text(
                    message,
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      fontFamily: 'Prompt',
                      fontSize: 11,
                      color: Color(0xFF282828),
                      fontWeight: FontWeight.w500,
                      letterSpacing: 0.2,
                    ),
                  ),
                  const SizedBox(
                    height: 20,
                  ),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      InkWell(
                        highlightColor: Colors.transparent,
                        splashColor: Colors.transparent,
                        onTap: () {
                          Navigator.of(context).pop(true);
                        },
                        child: Container(
                          alignment: Alignment.center,
                          width: 132,
                          height: 40,
                          decoration: BoxDecoration(
                            boxShadow: [
                              BoxShadow(
                                color:
                                const Color(0xFF000000).withOpacity(0.25),
                                spreadRadius: 0,
                                blurRadius: 4,
                                offset: const Offset(0, 2),
                              ),
                            ],
                            borderRadius: const BorderRadius.all(
                              Radius.circular(58),
                            ),
                            border: Border.all(
                              width: 2,
                            ),
                            color: const Color(0xFFFFB100),
                          ),
                          child: Text(
                            btnText1,
                            textAlign: TextAlign.center,
                            style: const TextStyle(
                              fontFamily: 'Prompt-Medium',
                              fontSize: 13,
                              color: Color(0xFFFFFFFF),
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(
                    height: 5,
                  ),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      InkWell(
                        highlightColor: Colors.transparent,
                        splashColor: Colors.transparent,
                        onTap: () {
                          Navigator.of(context).pop(false);
                        },
                        child: Container(
                          alignment: Alignment.center,
                          width: 132,
                          height: 40,
                          decoration: BoxDecoration(
                            borderRadius: const BorderRadius.all(
                              Radius.circular(58),
                            ),
                            border: Border.all(
                              width: 2,
                            ),
                            color: const Color(0xFF282828),
                          ),
                          child: Text(
                            btnText2,
                            textAlign: TextAlign.center,
                            style: const TextStyle(
                              fontFamily: 'Prompt-Medium',
                              fontSize: 13,
                              color: Color(0xFFFFFFFF),
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(
                    height: 30,
                  ),
                ],
              ),
            ),
          );
        });
  }

  static showNewAccept(context, title, message, btnText) {
    return showDialog(
        barrierColor: Colors.transparent,
        barrierDismissible: true,
        context: context,
        builder: (BuildContext context) {
          return Center(
            child: AlertDialog(
              backgroundColor: Colors.white.withOpacity(0.98),
              titlePadding: const EdgeInsets.fromLTRB(25, 30, 25, 0),
              contentPadding: const EdgeInsets.fromLTRB(25, 12, 25, 0),
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.all(
                  Radius.circular(20),
                ),
              ),
              title: Image.asset(
                'assets/icon/contact_icon.png',
                height: 43.74,
                width: 50,
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    title,
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      fontFamily: 'Prompt-Medium',
                      fontSize: 16,
                      color: Color(0xFF000000),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  Text(
                    message,
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      fontFamily: 'Prompt',
                      fontSize: 12,
                      color: Color(0xFF282828),
                      fontWeight: FontWeight.w500,
                      letterSpacing: 0.2,
                    ),
                  ),
                  const SizedBox(
                    height: 20,
                  ),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      InkWell(
                        highlightColor: Colors.transparent,
                        splashColor: Colors.transparent,
                        onTap: () {
                          Navigator.of(context).pop(true);
                        },
                        child: Container(
                          alignment: Alignment.center,
                          width: 132,
                          height: 40,
                          decoration: BoxDecoration(
                            boxShadow: [
                              BoxShadow(
                                color:
                                const Color(0xFF000000).withOpacity(0.25),
                                spreadRadius: 0,
                                blurRadius: 4,
                                offset: const Offset(0, 2),
                              ),
                            ],
                            borderRadius: const BorderRadius.all(
                              Radius.circular(58),
                            ),
                            border: Border.all(
                              width: 2,
                            ),
                            color: const Color(0xFFFFB100),
                          ),
                          child: Text(
                            btnText,
                            textAlign: TextAlign.center,
                            style: const TextStyle(
                              fontFamily: 'Prompt-Medium',
                              fontSize: 13,
                              color: Color(0xFFFFFFFF),
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(
                    height: 30,
                  ),
                ],
              ),
            ),
          );
        });
  }

  static showTutorial(context, title, message, btnText1, btnText2) {
    final tutorialCtl =  Get.find<TutorialController>();
    final psiCtl = Get.find<PsiController>();
    return showDialog(
      barrierColor: Colors.transparent,
      barrierDismissible: false,
      context: context,
      builder: (BuildContext context) {
        return Material(
          color: Colors.transparent,
          child: Center(
            child: Container(
              height: 295.h,
              width: 284.w,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF000000).withOpacity(0.2),
                    blurRadius: 50,
                    offset: const Offset(0, 4),
                  )
                ],
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Image.asset(
                    'assets/icon/contact_icon.png',
                    height: 43.74,
                    width: 50,
                  ),
                  Text(
                    title,
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      fontFamily: 'Prompt-Medium',
                      fontSize: 14,
                      color: Color(0xFF000000),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 10),
                  Text(
                    message,
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      fontFamily: 'Prompt',
                      fontSize: 11,
                      color: Color(0xFF282828),
                      fontWeight: FontWeight.w500,
                      letterSpacing: 0.2,
                    ),
                  ),
                  const SizedBox(height: 20),
                  InkWell(
                    highlightColor: Colors.transparent,
                    splashColor: Colors.transparent,
                    onTap: () {
                      tutorialCtl.clearAllTutorials();
                      Navigator.of(context).pop(true);
                      psiCtl.getCarOwner();
                      Get.offAll(() => const  HomeNavigator());
                    },
                    child: Container(
                      alignment: Alignment.center,
                      width: 132,
                      height: 40,
                      decoration: BoxDecoration(
                        boxShadow: [
                          BoxShadow(
                            color: const Color(0xFF000000).withOpacity(0.25),
                            spreadRadius: 0,
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                        borderRadius:
                        const BorderRadius.all(Radius.circular(58)),
                        border: Border.all(width: 2),
                        color: const Color(0xFFFFB100),
                      ),
                      child: Text(
                        btnText1,
                        textAlign: TextAlign.center,
                        style: const TextStyle(
                          fontFamily: 'Prompt-Medium',
                          fontSize: 13,
                          color: Color(0xFFFFFFFF),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(
                    height: 10.h,
                  ),
                  InkWell(
                    highlightColor: Colors.transparent,
                    splashColor: Colors.transparent,
                    onTap: () {
                      Navigator.of(context).pop(false);
                    },
                    child: Container(
                      alignment: Alignment.center,
                      width: 132,
                      height: 40,
                      decoration: BoxDecoration(
                        borderRadius:
                        const BorderRadius.all(Radius.circular(58)),
                        border: Border.all(width: 2),
                        color: const Color(0xFF282828),
                      ),
                      child: Text(
                        btnText2,
                        textAlign: TextAlign.center,
                        style: const TextStyle(
                          fontFamily: 'Prompt-Medium',
                          fontSize: 13,
                          color: Color(0xFFFFFFFF),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}