import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:rive/rive.dart';
import 'package:loading_animations/loading_animations.dart';

class AppLoader {
  static loader(context) {
    showDialog(
      barrierDismissible: false,
      context: context,
      builder: (BuildContext context) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const SizedBox(
                height: 136,
                child: RiveAnimation.asset('assets/animatedlogo/Chang-running.riv'),
              ),
              const SizedBox(
                height: 10,
              ),
              LoadingJumpingLine.circle(
                borderColor: Colors.white,
                size: 30,
                backgroundColor: Colors.white,
                duration: const Duration(milliseconds: 800),
              )
            ],
          ),
        );
      }
    );
  }

  static loaderChat(context) {
    showDialog(
        barrierDismissible: false,
        context: context,
        barrierColor:   Color(0xFFFFB100),
        builder: (BuildContext context) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Stack(
                  children: [
                    // Container(
                    //   width: Get.width,
                    //   height: Get.height,
                    //   color: Color(0xFFFFB100),
                    // ),
                    Column(
                      children: [
                        const SizedBox(
                          height: 136,
                          child: RiveAnimation.asset('assets/animatedlogo/Chang-running.riv'),
                        ),
                        const SizedBox(
                          height: 10,
                        ),
                        LoadingJumpingLine.circle(
                          borderColor: Colors.white,
                          size: 30,
                          backgroundColor: Colors.white,
                          duration: const Duration(milliseconds: 800),
                        ),
                      ],
                    )
                  ],
                )
              ],
            ),
          );
        }
    );
  }

  static loaderWaitPage(context) {
          return Material(
            child: Stack(
              children: [
                Container(
                  width: Get.width,
                  height: Get.height,
                  decoration: const BoxDecoration(
                    color: Color(0xFFFFB100),
                  ),
                ),
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const SizedBox(
                      height: 136,
                      child: RiveAnimation.asset('assets/animatedlogo/Chang-running.riv'),
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                    LoadingJumpingLine.circle(
                      borderColor: Colors.white,
                      size: 30,
                      backgroundColor: Colors.white,
                      duration: const Duration(milliseconds: 800),
                    )
                  ],
                )
              ],
            ),
          );
  }

  static dismiss(context) {
    try {
      // Force dismiss all dialogs
      while (Navigator.of(context, rootNavigator: true).canPop()) {
        Navigator.of(context, rootNavigator: true).pop();
      }
      print('✅ All loaders dismissed');
    } catch (e) {
      print('⚠️ Error dismissing loader: $e');
    }
  }

  static forceDismissAll(context) {
    try {
      // More aggressive dismissal
      Navigator.of(context, rootNavigator: true).popUntil((route) => route.isFirst);
      print('✅ Force dismissed all dialogs');
    } catch (e) {
      print('⚠️ Error force dismissing: $e');
    }
  }


}