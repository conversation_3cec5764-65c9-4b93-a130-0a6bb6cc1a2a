import 'package:get/get.dart';
import 'package:flutter/foundation.dart';

class NavigationHelper {
  static void navigateTo(String routeName, {dynamic arguments}) {
    try {
      if (kDebugMode) print('🧭 Navigating to: $routeName');
      
      if (Get.currentRoute == routeName) {
        if (kDebugMode) print('⚠️ Already on route: $routeName');
        return;
      }
      
      Get.toNamed(routeName, arguments: arguments);
    } catch (e) {
      if (kDebugMode) print('❌ Navigation error to $routeName: $e');
      
      // Fallback navigation
      try {
        Get.back();
      } catch (fallbackError) {
        if (kDebugMode) print('❌ Fallback navigation error: $fallbackError');
      }
    }
  }
  
  static void navigateAndReplace(String routeName, {dynamic arguments}) {
    try {
      if (kDebugMode) print('🧭 Navigate and replace to: $routeName');
      Get.offNamed(routeName, arguments: arguments);
    } catch (e) {
      if (kDebugMode) print('❌ Navigate and replace error to $routeName: $e');
    }
  }
  
  static void navigateAndClearStack(String routeName, {dynamic arguments}) {
    try {
      if (kDebugMode) print('🧭 Navigate and clear stack to: $routeName');
      Get.offAllNamed(routeName, arguments: arguments);
    } catch (e) {
      if (kDebugMode) print('❌ Navigate and clear stack error to $routeName: $e');
    }
  }
}