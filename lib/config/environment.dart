class Environment {
  static const String environment = String.fromEnvironment(
    'ENVIRONMENT',
    defaultValue: 'development',
  );
  
  static const String apiUrl = String.fromEnvironment(
    'API_URL',
    defaultValue: 'https://api.prachakij.com',
  );
  
  static bool get isProduction => environment == 'production';
  static bool get isStaging => environment == 'staging';
  static bool get isDevelopment => environment == 'development';
  
  // API Endpoints
  static String get baseApiUrl => apiUrl;
  
  // Firebase Configuration
  static String get firebaseProjectId => 'mapp-pms';
  static String get firebaseSiteId => 'mapp-prachakij';
  
  // App Configuration
  static bool get enableDebugLogs => !isProduction;
  static bool get enableAnalytics => isProduction;
  static bool get enableCrashlytics => isProduction;
  
  // Cache Configuration
  static Duration get cacheTimeout => isProduction 
      ? Duration(hours: 1) 
      : Duration(minutes: 5);
  
  // Network Configuration
  static Duration get networkTimeout => Duration(seconds: 30);
  static int get maxRetries => 3;
  
  // Feature Flags
  static bool get enableTestNavigation => !isProduction;
  static bool get enableMockData => isDevelopment;
  
  static void printEnvironmentInfo() {
    if (enableDebugLogs) {
      print('🌍 Environment Configuration:');
      print('  - Environment: $environment');
      print('  - API URL: $apiUrl');
      print('  - Firebase Project: $firebaseProjectId');
      print('  - Debug Logs: $enableDebugLogs');
      print('  - Analytics: $enableAnalytics');
      print('  - Test Navigation: $enableTestNavigation');
      print('  ================================');
    }
  }
}
