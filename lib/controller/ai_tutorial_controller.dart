import 'dart:convert';
import 'dart:math';
import 'package:flutter/cupertino.dart';
import 'package:http/http.dart' as http;
import 'package:get/get.dart';
import 'package:get/get_rx/get_rx.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl/intl.dart';
import 'package:mapp_prachakij_v3/component/url.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';
import 'package:mapp_prachakij_v3/controller/tutorial_controller.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:crypto/crypto.dart';

class AiTutorialController extends GetxController
    with GetTickerProviderStateMixin {
  RxInt currentBubbleIndex = 0.obs;
  final storage = GetStorage();

  RxList<Map<String, dynamic>> dailyDashboardList =
      <Map<String, dynamic>>[].obs;

  final randomAnyItem = <String, dynamic>{}.obs;
  final randomCategory = ''.obs;
  static const String tutorialKey = 'lastShownDate';
  final allItems = <Map<String, dynamic>>[];
  RxBool isLoading = true.obs;
  List<Map<String, dynamic>> pickedItems = [];
  String pickedDateKey = 'picked_date';
  String pickedItemKey = 'picked_item';

  final List<Map<String, dynamic>> bubbleDataList = [
    {
      "text": "สะสมคะแนน PMSpoint นี่เลย!",
      "offset": 20.0,
      "width": 290.0,
      "arrowPosition": "top", // 👈 อยู่บนกล่อง
      "top": 200.0, // 👈 ตำแหน่งจากบน
    },
    {
      "text": "แนะนำซื้อรถโดยคะแนน\n300,000 PMSP",
      "offset": 80.0,
      "width": 343.0,
      "arrowPosition": "bottom", // 👈 อยู่ล่างกล่อง
      "top": 700.0, // 👈 ตำแหน่งจากบน
    },
    {
      "text": "ตรวจสอบคะแนน PMSpoint ของท่านได้\nนี้",
      "offset": 80.0,
      "width": 343.0,
      "arrowPosition": "top", // 👈 อยู่บนกล่อง
      "top": 200.0, // 👈 ตำแหน่งจากบน
    },
    {
      "text": "อย่า \"โชด\" ทายเลข\" ครั้งถัดไป",
      "offset": 80.0,
      "width": 343.0,
      "arrowPosition": "top", // 👈 อยู่บนกล่อง
      "top": 200.0, // 👈 ตำแหน่งจากบน
    }
    // {
    //   "text": "เคลือบแก้ว! แถม! กตาน้ำหอม",
    //   "offset": 270.0,
    //   "width": 325.0,
    //   "arrowPosition": "bottom", // 👈 อยู่ล่างกล่อง
    //   "top": 500.0, // 👈 ตำแหน่งจากบน
    // },
    // {
    //   "text": "เคลมรถ! แต่ได้! ได้",
    //   "offset": 120.0,
    //   "width": 284.0,
    //   "arrowPosition": "bottom", // 👈 อยู่ล่างกล่อง
    //   "top": 620.0, // 👈 ตำแหน่งจากบน
    // },
  ];
  final profileCtl = Get.find<ProfileController>();
  final selectedItemDisplayText = ''.obs;
  RxBool showTutorialBox = true.obs;
  RxBool shouldShowTutorialToday = false.obs;
  final box = GetStorage(); // อยู่ใน AiTutorialController ก็ได้

  @override
  void onReady() async {
    super.onReady();
    print('⏱️ onReady called');
    currentBubbleIndex.value = -1; // 💡 UI แสดง

    // 🔧 รอให้ ProfileController พร้อมก่อน
    await waitForProfileController();

    await startBubbleSequence();
    dailyDashboard();
    // resetAndPick();
  }

  String generateDataHash(List<Map<String, dynamic>> list) {
    final jsonString = jsonEncode(list);
    final bytes = utf8.encode(jsonString);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  // 🔧 รอให้ ProfileController พร้อม
  Future<void> waitForProfileController() async {
    print('🔄 Waiting for ProfileController to be ready...');

    // รอ 10 วินาที
    int maxWaitTime = 10000; // 10 วินาที
    int waitInterval = 500; // 0.5 วินาที
    int currentWaitTime = 0;

    while (currentWaitTime < maxWaitTime) {
      try {
        // ตรวจสอบว่า ProfileController พร้อมหรือไม่
        if (profileCtl.token.value != null &&
            profileCtl.profile.value.mobile != null &&
            profileCtl.profile.value.mobile!.isNotEmpty) {
          print('✅ ProfileController is ready!');
          print('📱 Phone: ${profileCtl.profile.value.mobile}');
          print(
              '🔑 Token: ${profileCtl.token.value != null ? "Available" : "Null"}');
          return;
        }

        await Future.delayed(Duration(milliseconds: waitInterval));
        currentWaitTime += waitInterval;
        print('⏳ Still waiting... (${currentWaitTime}ms)');
      } catch (e) {
        print('⚠️ Error while waiting for ProfileController: $e');
        await Future.delayed(Duration(milliseconds: waitInterval));
        currentWaitTime += waitInterval;
      }
    }

    print('⚠️ ProfileController not ready after ${maxWaitTime}ms');
  }

  Future<void> startBubbleSequence() async {
    final today = DateFormat('yyyy-MM-dd').format(DateTime.now());
    final lastShownDate = box.read<String>(tutorialKey);

    final showTutorial = box.read("tutorial_played_home");
    print("📅 Today: $today | Last shown: $lastShownDate");

    if (lastShownDate == today) {
      print("⏩ Tutorial already shown today. Skipping.");
      currentBubbleIndex.value = -1;
      return;
    }

    if (profileCtl.token.value == null) {
      print("⛔️ No token. Skipping tutorial.");
      currentBubbleIndex.value = -1;
      return;
    }

    if (showTutorial == false) {
      print("⛔️ Tutorial not enabled. Skipping.");
      currentBubbleIndex.value = -1;
      return;
    }

    currentBubbleIndex.value = 0;

    for (int i = 0; i < bubbleDataList.length; i++) {
      currentBubbleIndex.value = i;
      await Future.delayed(const Duration(seconds: 10));

      if (currentBubbleIndex.value == -1) {
        print("🛑 Tutorial cancelled by user.");
        return;
      }
    }

    saveShowAiTutorialDate();
    currentBubbleIndex.value = -1;
    print("✅ Finished AI tutorial.");
  }

  // Future<void> startBubbleSequence() async {
  //   for (int i = 0; i < bubbleDataList.length; i++) {
  //     if(profileCtl.token.value !=null){
  //       currentBubbleIndex.value = i;
  //       await Future.delayed(Duration(seconds: 10));
  //     }
  //   }
  //   currentBubbleIndex.value = -1; // ซ่อน bubble
  // }

  bool alreadyPicked = false;

  dailyDashboard() async {
    isLoading.value = true; // เริ่มโหลด
    dailyDashboardList.clear();
    print('📅 Daily Dashboard');

    // 🔧 ตรวจสอบข้อมูลก่อนส่ง API
    if (profileCtl.profile.value.mobile == null ||
        profileCtl.profile.value.mobile!.isEmpty) {
      print(
          '⚠️ Phone number is null or empty. Skipping dailyDashboard API call.');
      isLoading.value = false;
      return;
    }

    final url = Uri.parse(
        'https://n8n-pmsg.agilesoftgroup.com/webhook/PMS/dailydashboard');
    final body = {"phone": profileCtl.profile.value.mobile};

    // print('🌐 Requesting from: $url');
    // print('📦 Body: $body');

    try {
      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(body),
      );

      // print('📥 Response status: ${response.body}');
      final data = jsonDecode(response.body);
      final output = data[0]['output'];

      if (output['status'] == 200) {
        print('✅ Data received from n8n:');
        dailyDashboardList.add({"carOwner": output['cars']});
        dailyDashboardList.add({"promotions": output['promotions']});
        dailyDashboardList.add({"appointment": output['appointment']});
        dailyDashboardList.add({"event": output['event']});
        dailyDashboardList.refresh();
        print('📊 Daily Dashboard data: ${dailyDashboardList.length} items');

        print(dailyDashboardList);
        await pickRandomItem();
        update();
      } else {
        print('❌ Failed: ${response.statusCode}');
        print('⚠️ Body: ${response.body}');
      }
    } catch (e) {
      print('⚠️ Error: $e');
    } finally {
      isLoading.value = false; // ✅ โหลดเสร็จแล้ว
    }
  }

  // pickRandomItem() {
  //   allItems.clear();
  //   for (var item in dailyDashboardList) {
  //     final itemType = item.keys.first;
  //     print('📦 Checking item type: $itemType');
  //     if (item.values.first is List) {
  //       allItems.addAll(
  //           (item.values.first as List).map((e) => {'type': itemType, ...e}));
  //     } else {
  //       allItems.add({'type': itemType, ...item.values.first});
  //     }
  //   }
  //
  //   if (allItems.isNotEmpty) {
  //     final selectedItem = allItems[Random().nextInt(allItems.length)];
  //     // Update the selected item display text dynamically
  //     print('📦 Selected item type: ${selectedItem}');
  //     updateSelectedItem(selectedItem);
  //   } else {
  //     print('⚠️ No items available to pick randomly!');
  //     selectedItemDisplayText.value = "ไม่ข้อมูลแสดงได้";
  //   }
  // }
  Future<void> pickRandomItem() async {
    final prefs = await SharedPreferences.getInstance();
    final today = DateFormat('yyyy-MM-dd').format(DateTime.now());

    final lastPickedDate = prefs.getString(pickedDateKey);
    final savedItemString = prefs.getString(pickedItemKey);
    final lastDataHash = prefs.getString('dailyDashboardHash');

    // สร้าง hash ของข้อมูลใหม่
    final currentDataHash = generateDataHash(dailyDashboardList);

    if (lastPickedDate == today &&
        savedItemString != null &&
        lastDataHash == currentDataHash) {
      final savedItem = jsonDecode(savedItemString);
      print('📅 ทำไปแล้ว (ข้อมูลไม่เปลี่ยน) → $savedItem');
      updateSelectedItem(savedItem);
      return;
    }

    // ถ้า data เปลี่ยน hash ให้ทำใหม่
    allItems.clear();

    for (var item in dailyDashboardList) {
      final itemType = item.keys.first;
      final raw = item[itemType];

      if (raw is List) {
        for (var e in raw) {
          allItems.add({'type': itemType, ...e});
        }
      } else if (raw is Map<String, dynamic>) {
        allItems.add({'type': itemType, ...raw});
      }
    }

    if (allItems.isNotEmpty) {
      final selectedItem = allItems[Random().nextInt(allItems.length)];
      await prefs.setString(pickedDateKey, today);
      await prefs.setString(pickedItemKey, jsonEncode(selectedItem));
      await prefs.setString('dailyDashboardHash', currentDataHash);

      print('🎯 ทำใหม่วันนี้ (ข้อมูลเปลี่ยน) → $selectedItem');
      updateSelectedItem(selectedItem);
    } else {
      selectedItemDisplayText.value = "ไม่ข้อมูลแสดงได้";
      print('⚠️ ไม่มีข้อมูลให้ทำ');
    }
  }

  updateSelectedItem(Map<String, dynamic> item) {
    String displayText;
    switch (item['type']) {
      case 'promotions':
        displayText = '${item['namePromotion']}';
        break;
      case 'appointment':
        displayText = "${item['nameAppointment']}";
        break;
      case 'event':
        // displayText ="${item['name_activity'] ?? 'ไม่'} \n: ${item['fdate_activity'] ?? 'ไม่'} - ${item['edate_activity']}";
        displayText = "${item['nameEvent'] ?? 'ไม่'}";
        break;
      case 'carOwner':
        final nameService = item['nameNextServiceDate'];
        final nameRust = item['nameNextAntiRustDate'];

        final serviceText =
            (nameService != null && nameService.toString().isNotEmpty)
                ? nameService
                : "🔧 ไม่ข้อมูลเช็กระยะ";

        final rustText = (nameRust != null && nameRust.toString().isNotEmpty)
            ? nameRust
            : "🛡️ ไม่ข้อมูลพ่นสนิม";

        displayText = "$serviceText\n$rustText";

        print("🔍 nameNextServiceDate: $nameService");
        print("🔍 nameNextAntiRustDate: $nameRust");
        print("🧾 Final displayText: $displayText");
        break;

      default:
        displayText = "ไม่ทราบประเภท: ${item['type']}";
    }
    selectedItemDisplayText.value = displayText;
    print('📦 Updated selected item display text: $displayText');
  }

  saveShowAiTutorialDate() {
    final today = DateFormat('yyyy-MM-dd').format(DateTime.now());
    box.write(tutorialKey, today);
  }

  Future<void> resetAndPick() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(pickedItemKey);
    await prefs.remove(pickedDateKey);
    await prefs.remove('dailyDashboardHash');
    await pickRandomItem(); // ทำใหม่จากข้อมูล
  }
}
