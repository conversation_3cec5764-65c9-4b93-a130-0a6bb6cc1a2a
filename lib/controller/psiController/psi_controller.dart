import 'package:dart_extensions/dart_extensions.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:mapp_prachakij_v3/component/api.dart';
import 'package:mapp_prachakij_v3/component/secureStorage.dart';
import 'package:mapp_prachakij_v3/component/url.dart';
import 'package:mapp_prachakij_v3/controller/service/service.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';
import 'package:mapp_prachakij_v3/controller/tutorial_controller.dart';
import 'package:mapp_prachakij_v3/model/coupon_PSI.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../setting_controller/login_controller/login_controller.dart';

// ✅ แก้ไข import dart:html ให้ใช้เฉพาะเมื่อเป็น web
import 'dart:html' as html show window;

class PsiController extends GetxController {
  RxBool showTab = true.obs;
  RxList<Map<String, dynamic>> psiList = <Map<String, dynamic>>[].obs;
  CarOwnerModel carOwnerInfo = CarOwnerModel();
  List<String> keyList = [];
  RxInt index = 0.obs;
  RxString carRegSelect = "".obs;

  RxInt indexTicket = 0.obs;
  var isLoading = false.obs;
  RxList carPic = [].obs;
  RxList carPicLoader = [].obs;
  PageController pageDetailController = PageController();
  PageController pageDetailController2 = PageController();
  PageController pageDetailController3 = PageController();
  PageController pageDetailController4 = PageController();
  var storage = GetStorage();

  void changeShowTab(input) {
    showTab.value = input;
  }

  void changeIndexTicket(input) {
    indexTicket.value = input;
  }

  // ✅ แก้ไข getCarOwner - เอา popup ออก
  getCarOwner() async {
    try {
      print("🚗 Starting getCarOwner...");

      // ✅ เช็ค login status
      bool? loginStatus = await AppService.getPref('bool', 'loginStatus');
      if (loginStatus != true) {
        print("🚫 Not logged in, skipping getCarOwner");
        return;
      }

      isLoading(true);

      // ✅ ลองหาเบอร์โทรจากแหล่งต่างๆ
      var usedPhone = await _getPhoneFromMultipleSources();

      if (usedPhone == null || usedPhone.isEmpty) {
        print("❌ No phone number found from any source");
        isLoading(false);
        return;
      }

      print("📱 Using phone: $usedPhone");

      Map data = {"phone": usedPhone};
      final response =
          await AppApi.callAPIjwt("POST", AppUrl.getCarOwner, data);

      print("📡 getCarOwner API Response: ${response?['status']}");

      if (response != null && response['status'] == 200) {
        carOwnerInfo = CarOwnerModel.fromJson(response["result"]);

        print("✅ Car owner info loaded");
        print("🚗 Number of cars: ${carOwnerInfo.carList?.length ?? 0}");

        if (carOwnerInfo.carList != null && carOwnerInfo.carList!.isNotEmpty) {
          carPicLoader =
              List<bool>.filled(carOwnerInfo.carList!.length, true).obs;
          await getCarImg();

          // ✅  ProfileController lengthTab
          if (Get.isRegistered<ProfileController>()) {
            final profileCtl = Get.find<ProfileController>();
            if (profileCtl.token.value != null) {
              profileCtl.lengthTab.value = 5; // รถ = 5 tabs
              print("✅ Updated lengthTab to 5 (has car)");
            }
          }

          // ✅  tutorial controller
          if (Get.isRegistered<TutorialController>()) {
            final tutorialCtl = Get.find<TutorialController>();
            tutorialCtl.checkHomeStatus();
          }
        } else {
          print("❌ No cars found for user");

          // ✅  ProfileController lengthTab
          if (Get.isRegistered<ProfileController>()) {
            final profileCtl = Get.find<ProfileController>();
            if (profileCtl.token.value != null) {
              profileCtl.lengthTab.value = 4; // ไม่ได้ = 4 tabs
              print("✅ Updated lengthTab to 4 (no car)");
            }
          }
        }
      } else {
        print(
            "❌ getCarOwner API Error: ${response?['message'] ?? 'Unknown error'}");
      }
    } catch (e) {
      print("💥 Error in getCarOwner: $e");
    } finally {
      isLoading(false);
      update();
    }
  }

  // ✅ หาเบอร์โทรจากแหล่งต่างๆ
  Future<String?> _getPhoneFromMultipleSources() async {
    try {
      // 1. จาก ProfileController
      if (Get.isRegistered<ProfileController>()) {
        final profileCtl = Get.find<ProfileController>();
        if (profileCtl.profile.value.mobile != null &&
            profileCtl.profile.value.mobile!.isNotEmpty) {
          print(
              "📱 Phone from ProfileController: ${profileCtl.profile.value.mobile}");
          return profileCtl.profile.value.mobile.toString();
        }
      }

      // 2. จาก SecureStorage
      final secureStorage = SecureStorage();
      String? phoneFromSecure = await secureStorage.readSecureData('phone');
      if (phoneFromSecure != null && phoneFromSecure.isNotEmpty) {
        print("📱 Phone from SecureStorage: $phoneFromSecure");
        return phoneFromSecure;
      }

      // 3. จาก GetStorage
      final storage = GetStorage();
      String? phoneFromStorage = storage.read('phone');
      if (phoneFromStorage != null && phoneFromStorage.isNotEmpty) {
        print("📱 Phone from GetStorage: $phoneFromStorage");
        return phoneFromStorage;
      }

      // 4. จาก SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      String? phoneFromPrefs = prefs.getString('phone');
      if (phoneFromPrefs != null && phoneFromPrefs.isNotEmpty) {
        print("📱 Phone from SharedPreferences: $phoneFromPrefs");
        return phoneFromPrefs;
      }

      // 5. จาก Web localStorage
      if (kIsWeb) {
        String? phoneFromWeb = html.window.localStorage['phone'];
        if (phoneFromWeb != null && phoneFromWeb.isNotEmpty) {
          print("📱 Phone from Web localStorage: $phoneFromWeb");
          return phoneFromWeb;
        }
      }

      // 6. จาก getLocalPhone (method เลม)
      String? phoneFromLocal = await getLocalPhone();
      if (phoneFromLocal != null && phoneFromLocal.isNotEmpty) {
        print("📱 Phone from getLocalPhone: $phoneFromLocal");
        return phoneFromLocal;
      }

      return null;
    } catch (e) {
      print("❌ Error getting phone from multiple sources: $e");
      return null;
    }
  }

  // ✅ เล่ม function เช็ค login status
  Future<bool> _checkLoginStatus() async {
    try {
      // เช็คจาก SharedPreferences
      bool? loginStatus = await AppService.getPref('bool', 'loginStatus');
      if (loginStatus != true) {
        print("🚫 Login status is false");
        return false;
      }

      // เช็คจาก SecureStorage
      final secureStorage = SecureStorage();
      String? token = await secureStorage.readSecureData('auth_token');
      if (token == null || token.isEmpty) {
        print("🚫 No auth token found");
        return false;
      }

      print("✅ User is logged in");
      return true;
    } catch (e) {
      print("❌ Error checking login status: $e");
      return false;
    }
  }

  // ✅ ลองหาเบอร์จากแหล่งอื่น
  Future<String?> _tryGetPhoneFromOtherSources() async {
    try {
      print("🔍 Trying other phone sources...");

      // ลองจาก user profile
      try {
        if (Get.isRegistered<ProfileController>()) {
          final profileController = Get.find<ProfileController>();
          if (profileController.profile.value.mobile != null) {
            String phone = profileController.profile.value.mobile.toString();
            if (phone.isNotEmpty && phone != 'null') {
              print("📱 Phone from profile: $phone");
              await _savePhone(phone); // ไว้ใช้ครั้งหน้า
              return phone;
            }
          }
        }
      } catch (e) {
        print("❌ Error getting phone from profile: $e");
      }

      // ลองจาก SharedPreferences
      try {
        final prefs = await SharedPreferences.getInstance();
        String? phone = prefs.getString('phone');
        if (phone != null && phone.isNotEmpty && phone != 'null') {
          print("📱 Phone from SharedPreferences: $phone");
          return phone;
        }
      } catch (e) {
        print("❌ Error getting phone from SharedPreferences: $e");
      }

      // ลองจาก storage เก่า
      try {
        var oldPhone = storage.read("phone_profile");
        if (oldPhone != null &&
            oldPhone.toString().isNotEmpty &&
            oldPhone.toString() != 'null') {
          print("📱 Phone from old storage: $oldPhone");
          await _savePhone(oldPhone.toString()); // ไว้ใช้ครั้งหน้า
          return oldPhone.toString();
        }
      } catch (e) {
        print("❌ Error getting phone from old storage: $e");
      }

      return null;
    } catch (e) {
      print("❌ Error trying other phone sources: $e");
      return null;
    }
  }

  // ✅  เบอร์ (เก็บไว้หาเจอจากแหล่งอื่น)
  Future<void> _savePhone(String phone) async {
    try {
      // ใน SecureStorage
      await SecureStorage().writeSecureData('phone', phone);

      // ใน GetStorage
      final storage = GetStorage();
      await storage.write('phone', phone);

      // ใน localStorage web
      if (kIsWeb) {
        html.window.localStorage['phone'] = phone;
      }

      // ใน SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('phone', phone);

      print("✅ Phone saved: $phone");
    } catch (e) {
      print("❌ Error saving phone: $e");
    }
  }

  // ✅ แก้ไข getCouponPSI ให้เช็ค login เหมือน
  getCouponPSI() async {
    try {
      // ✅ เช็ค login status ก่อน
      bool isLoggedIn = await _checkLoginStatus();
      if (!isLoggedIn) {
        print("🚫 Not logged in, skipping getCouponPSI");
        return;
      }

      psiList.clear();
      var usedPhone = await getLocalPhone();

      if (usedPhone == null || usedPhone.isEmpty) {
        print("❌ Phone number not found for getCouponPSI");
        return;
      }

      Map data = {
        "phone": usedPhone,
      };

      final response =
          await AppApi.callAPIjwt("POST", AppUrl.getCouponPSI, data);

      if (response['status'] == 200) {
        if (response['result'] != null) {
          response['result'].forEach((key, value) {
            if (value['statusCode'] == 200) {
              psiList.add({
                'engine': key,
                'checkPSI': true,
                'dataCoupon': value['dataCoupon'],
                'dataCustomer': value['dataCustomer'],
              });
            } else {
              psiList.add({
                'engine': key,
                'checkPSI': false,
                'dataCoupon': null,
                'dataCustomer': null,
              });
            }
          });
        } else {
          print("Response result is null");
        }
      }
      update();
    } catch (e) {
      print("Exception occurred in getCouponPSI: $e");
    }
  }

  // ✅ แก้ไข getLocalPhone ให้หลาย platform
  Future<String?> getLocalPhone() async {
    try {
      print("🔍 Checking phone storage...");
      String? phone;

      if (kIsWeb) {
        // web
        phone = await SecureStorage().readSecureData('phone');
        print("📱 Web phone from secure storage: $phone");

        if (phone == null || phone.isEmpty) {
          final storage = GetStorage();
          phone = storage.read('phone');
          print("📱 Web phone from GetStorage: $phone");
        }

        if (phone == null || phone.isEmpty) {
          phone = html.window.localStorage['phone'];
          print("📱 Web phone from localStorage: $phone");
        }
      } else {
        // mobile
        phone = await SecureStorage().readSecureData('phone');
        print("📱 Mobile phone from secure storage: $phone");

        if (phone == null || phone.isEmpty) {
          final storage = GetStorage();
          phone = storage.read('phone');
          print("📱 Mobile phone from GetStorage: $phone");
        }
      }

      print("📱 Final phone result: $phone");
      return phone;
    } catch (e) {
      print("❌ Error getting local phone: $e");
      return null;
    }
  }

  getCarImg() async {
    for (int i = 0; i < carOwnerInfo.carList!.length; i++) {
      if (carOwnerInfo.carList![i].colorCode != "" &&
          carOwnerInfo.carList![i].carModel != "") {
        try {
          Map data = {
            "colorCode": carOwnerInfo.carList![i].colorCode,
            "car_model": carOwnerInfo.carList![i].carModel
          };
          final response =
              await AppApi.callAPIjwt("POST", AppUrl.getCarImg, data);
          if (response['status'] == 200) {
            carPic.add(response["result"]["image_url"]);
            carPicLoader[i] = false;
          }
        } catch (e) {
          print("Error getCarImg: $e");
          carPic.add("");
          carPicLoader[i] = false;
        }
      } else {
        carPic.add("");
        carPicLoader[i] = false;
      }
    }
    update();
  }

  void scrollToPage(int pageIndex) {
    pageDetailController.animateToPage(pageIndex,
        duration: const Duration(milliseconds: 600), curve: Curves.easeInOut);
  }

  void scrollToPage2(int pageIndex) {
    pageDetailController2.animateToPage(pageIndex,
        duration: const Duration(milliseconds: 700), curve: Curves.easeInOut);
  }

  void scrollToPage3(int pageIndex) {
    pageDetailController3.animateToPage(pageIndex,
        duration: const Duration(milliseconds: 800), curve: Curves.easeInOut);
  }

  void scrollToPage4(int pageIndex) {
    pageDetailController4.animateToPage(pageIndex,
        duration: const Duration(milliseconds: 900), curve: Curves.easeInOut);
  }

  dateEngToThaiMiniPlus6M(String date) {
    List<String> months_th_mini = [
      "",
      "ม.ค.",
      "ก.พ.",
      "มี.ค.",
      "เม.ย.",
      "พ.ค.",
      "มิ.ย.",
      "ก.ค.",
      "ส.ค.",
      "ก.ย.",
      "ต.ค.",
      "พ.ย.",
      "ธ.ค.",
    ];
    DateTime parsedDate = DateTime.parse(date);
    DateTime currentDate = DateTime.now();
    parsedDate = parsedDate.add(const Duration(days: 30 * 6));
    int day = parsedDate.day;
    int month = parsedDate.month;
    int year = parsedDate.year + 543;
    if (month > 12) {
      month -= 12;
      year += 1;
    }
    DateTime parsedDatePlus6M = DateTime(parsedDate.year, month, day);
    int daysDifference = currentDate.difference(parsedDatePlus6M).inDays;
    if (daysDifference > 0) {
      if (daysDifference <= 30) {
        return " $daysDifference";
      } else {
        return "$day ${months_th_mini[month]} $year";
      }
    } else if (daysDifference == 0) {
      return "ต้องการ";
    } else {
      return "$day ${months_th_mini[month]} $year";
    }
  }

  dateEngToThaiMiniPlus1Y(String date) {
    List<String> months_th_mini = [
      "",
      "ม.ค.",
      "ก.พ.",
      "มี.ค.",
      "เม.ย.",
      "พ.ค.",
      "มิ.ย.",
      "ก.ค.",
      "ส.ค.",
      "ก.ย.",
      "ต.ค.",
      "พ.ย.",
      "ธ.ค.",
    ];
    DateTime parsedDate = DateTime.parse(date);
    DateTime currentDate = DateTime.now();
    parsedDate = parsedDate.add(const Duration(days: 30 * 12));
    int day = parsedDate.day;
    int month = parsedDate.month;
    int year = parsedDate.year + 543;
    if (month > 12) {
      month -= 12;
      year += 1;
    }
    DateTime parsedDatePlus6M = DateTime(parsedDate.year, month, day);
    int daysDifference = currentDate.difference(parsedDatePlus6M).inDays;
    if (daysDifference > 0) {
      if (daysDifference <= 30) {
        return " $daysDifference";
      } else {
        return "$day ${months_th_mini[month]} $year";
      }
    } else if (daysDifference == 0) {
      return "ต้องการ";
    } else {
      return "$day ${months_th_mini[month]} $year";
    }
  }

  dateEngToThaiMini(String date) {
    List<String> months_th_mini = [
      "",
      "ม.ค.",
      "ก.พ.",
      "มี.ค.",
      "เม.ย.",
      "พ.ค.",
      "มิ.ย.",
      "ก.ค.",
      "ส.ค.",
      "ก.ย.",
      "ต.ค.",
      "พ.ย.",
      "ธ.ค.",
    ];
    DateTime parsedDate = DateTime.parse(date);
    DateTime currentDate = DateTime.now();
    int day = parsedDate.day;
    int month = parsedDate.month;
    int year = parsedDate.year + 543;
    if (month > 12) {
      month -= 12;
      year += 1;
    }
    DateTime parsedDatePlus6M = DateTime(parsedDate.year, month, day);
    int daysDifference = currentDate.difference(parsedDatePlus6M).inDays;
    if (daysDifference > 0) {
      if (daysDifference <= 30) {
        return " $daysDifference";
      } else {
        return "$day ${months_th_mini[month]} $year";
      }
    } else if (daysDifference == 0) {
      return "ต้องการ";
    } else {
      return "$day ${months_th_mini[month]} $year";
    }
  }
}
