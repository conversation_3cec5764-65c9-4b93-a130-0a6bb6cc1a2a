import 'dart:ui';
import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import 'package:mapp_prachakij_v3/component/ai_tutorial.dart';
import 'package:mapp_prachakij_v3/component/loader.dart';
import 'package:mapp_prachakij_v3/component/secureStorage.dart';
import 'package:mapp_prachakij_v3/component/tutorial.dart';
import 'package:mapp_prachakij_v3/component/widget.dart';
import 'package:mapp_prachakij_v3/controller/ai_tutorial_controller.dart';
import 'package:mapp_prachakij_v3/controller/psiController/psi_controller.dart';
import 'package:mapp_prachakij_v3/controller/service/service.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/MR_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/chat_in_app_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/likepoint_controller/likepoint_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/login_controller/login_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/new_car_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/page_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/setting_controller.dart';
import 'package:mapp_prachakij_v3/controller/storage/storage_controller.dart';
import 'package:mapp_prachakij_v3/main.dart';
import 'package:mapp_prachakij_v3/view/chat_in_app/webview_tg.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/home_news.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/notification.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/home_promotion.dart';
import 'package:mapp_prachakij_v3/view/login/login.dart';
import 'package:mapp_prachakij_v3/view/mobile/home_mobile.dart';
import 'package:mapp_prachakij_v3/view/profile/profile.dart';
import 'package:mapp_prachakij_v3/view/psi_page/myPSI.dart';
import 'package:shorebird_code_push/shorebird_code_push.dart';
import 'package:simple_gradient_text/simple_gradient_text.dart';
import 'dart:html' as html;
import '../../view/home/<USER>';
import '../couponRestaurant_Cafe/couponR_C.controller.dart';
import '../setting_controller/lifetime_appointment_controller.dart';
import '../tutorial_controller.dart';

class MobileBodyPage extends StatefulWidget {
  const MobileBodyPage({Key? key}) : super(key: key);

  @override
  State<MobileBodyPage> createState() => _MobileBodyPageState();
}

class _MobileBodyPageState extends State<MobileBodyPage>
    with TickerProviderStateMixin {
  // ✅ ใช้ TickerProviderStateMixin แทน
  TabController? homeTabController;
  int currentIndex = 0;
  int flexTap = 5;

  // Controllers - ✅ ใช้ Get.find แทน Get.put เลื่อนหลีกเลี่ยงการสร้างซ้ำ
  late final ProfileController profileCtl;
  late final LoginController loginCtl;
  late final PageSelectController pageCtl;
  late final SettingController centerCtl;
  late final LikePointController likePointCtl;
  late final ReferralMRController mrCtl;
  late final PsiController PsiCtl;
  late final TutorialController tutorialCtl;
  late final ChatInAppController chatInAppCtl;
  final shorebirdCodePush = ShorebirdCodePush();

  @override
  void initState() {
    super.initState();
    print("🚀 Initializing MobileBodyPage...");

    // ✅ ตรวจสอบ URL และ handle register link
    _handleUrlNavigation();

    // Initialize controllers
    profileCtl = Get.find<ProfileController>();
    loginCtl = Get.find<LoginController>();
    pageCtl = Get.find<PageSelectController>();
    centerCtl = Get.find<SettingController>();
    likePointCtl = Get.find<LikePointController>();
    mrCtl = Get.find<ReferralMRController>();
    PsiCtl = Get.find<PsiController>();
    tutorialCtl = Get.find<TutorialController>();
    chatInAppCtl = Get.find<ChatInAppController>();

    _initializeApp();
  }

  void _handleUrlNavigation() {
    if (kIsWeb) {
      final uri = Uri.parse(html.window.location.href);
      print("🌐 Current URL: ${uri.toString()}");

      // ✅ ตรวจสอบ register URL with ref parameter
      if (uri.path.contains('/register') &&
          uri.queryParameters.containsKey('ref')) {
        String refCode = uri.queryParameters['ref'] ?? '';
        print("🔗 Register URL with ref: $refCode");

        // ✅ ตั้งค่า login controller
        loginCtl.typeMenu.value = 'register';
        loginCtl.page.value = 'register';

        // ✅ ใช้ WidgetsBinding แทน Future.delayed
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (Get.context != null) {
            Get.toNamed('/register', parameters: {'ref': refCode});
          }
        });
        return;
      }

      // ✅ ตรวจสอบ URL อื่นๆ
      if (uri.path == '/register') {
        loginCtl.typeMenu.value = 'register';
        loginCtl.page.value = 'register';
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (Get.context != null) {
            Get.toNamed('/register');
          }
        });
        return;
      }

      if (uri.path == '/login') {
        loginCtl.typeMenu.value = 'login';
        loginCtl.page.value = 'login';
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (Get.context != null) {
            Get.toNamed('/login');
          }
        });
        return;
      }
    }
  }

  Future<void> _initializeApp() async {
    try {
      print("🔄 Starting app initialization with timeout...");

      // ✅ Timeout สำหรับ initialization (10 วินาที)
      await Future.any([
        _performInitialization(),
        Future.delayed(Duration(seconds: 10), () {
          throw TimeoutException('Initialization timeout', Duration(seconds: 10));
        })
      ]);

    } catch (e) {
      print("❌ Error initializing MobileBodyPage: $e");
      // ✅ Fallback - ใช้ guest mode
      _fallbackInitialization();
    }
  }

  Future<void> _performInitialization() async {
    print("🔄 Starting simplified initialization...");

    // ✅ ตรวจสอบ login status
    bool? loginStatus = await AppService.getPref('bool', 'loginStatus');
    print("🔐 Login status: $loginStatus");

    // ✅ กำหนดค่าเริ่มต้น tab length แบบ simple
    flexTap = loginStatus == true ? 4 : 3;
    print("📊 Initial tab length: $flexTap");

    // ✅ สร้าง TabController เพียงครั้งเดียว
    if (homeTabController != null) {
      homeTabController!.dispose();
    }
    homeTabController = TabController(vsync: this, length: flexTap);
    homeTabController!.addListener(_handleTabSelection);

    if (loginStatus == true) {
      try {
        // ✅ โหลดข้อมูลแบบ sequential เพื่อป้องกัน race conditions
        print("🔄 Loading profile data...");
        await profileCtl.getProfileAndMR();
        print("✅ Profile loaded");

        // ✅ รอสักครู่ให้ profile data settle
        await Future.delayed(Duration(milliseconds: 200));

        print("🔄 Loading car data...");
        await PsiCtl.getCarOwner();
        print("✅ Car data loaded");

        // ✅ ตรวจสอบรถและปรับ tab length
        bool hasCar = PsiCtl.carOwnerInfo.carList != null &&
            PsiCtl.carOwnerInfo.carList!.isNotEmpty;

        int newFlexTap = hasCar ? 5 : 4;
        print("🚗 Has car: $hasCar, New tab length: $newFlexTap");

        // ✅ อัปเดต TabController ถ้าจำเป็น
        if (newFlexTap != flexTap) {
          flexTap = newFlexTap;
          homeTabController?.dispose();
          homeTabController = TabController(vsync: this, length: flexTap);
          homeTabController!.addListener(_handleTabSelection);
          print("🔄 TabController updated to length: $flexTap");
        }
      } catch (e) {
        print("❌ Error loading user data: $e");
        // ใช้ default tab length
      }
    }

    // ✅ โหลดข้อมูลอื่นๆ แบบ background (ไม่ block UI)
    _loadBackgroundData();

      print("✅ MobileBodyPage initialization completed with $flexTap tabs");

      // ✅ Force update UI
      if (mounted) setState(() {});
  }

  void _fallbackInitialization() {
    print("🔄 Using fallback initialization...");
    try {
      flexTap = 3; // Guest mode
      if (mounted) {
        homeTabController?.dispose();
        homeTabController = TabController(length: flexTap, vsync: this);
        homeTabController!.addListener(_handleTabSelection);
        setState(() {});
        print("✅ Fallback initialization completed with $flexTap tabs");
      }
    } catch (e) {
      print("❌ Fallback initialization error: $e");
    }
  }

  void _loadBackgroundData() {
    // ✅ โหลดข้อมูลแบบ background ไม่ block UI
    Future.delayed(Duration(milliseconds: 500), () async {
      try {
        print("🔄 Loading background data...");

        // Version check
        try {
          centerCtl.getVersion(context);
          print("✅ Version check completed");
        } catch (e) {
          print("⚠️ Version check error: $e");
        }

        // Update check
        try {
          _checkForUpdates(context);
          print("✅ Update check completed");
        } catch (e) {
          print("⚠️ Update check error: $e");
        }

        // PSI coupon (ถ้ามีรถ)
        try {
          if (Get.isRegistered<PsiController>() && flexTap >= 5) {
            final psiCtl = Get.find<PsiController>();
            psiCtl.getCouponPSI();
            print("✅ PSI coupon check completed");
          }
        } catch (e) {
          print("⚠️ PSI coupon error: $e");
        }

        // Tutorial status (manual update แทน reactive)
        try {
          if (Get.isRegistered<TutorialController>()) {
            final tutorialCtl = Get.find<TutorialController>();
            tutorialCtl.manualUpdate();
            print("✅ Tutorial manual update completed");
          }
        } catch (e) {
          print("⚠️ Tutorial update error: $e");
        }

        print("✅ Background data loading completed");
      } catch (e) {
        print("❌ Background loading error: $e");
      }
    });
  }

  void _handleTabSelection() {
    if (mounted) {
      setState(() {
        currentIndex = homeTabController?.index ?? 0;
      });
    }
  }

  // ✅ เลื่อน method _checkForUpdates
  Future<void> _checkForUpdates(BuildContext context) async {
    try {
      // Check whether a patch is available to install.
      final isUpdateAvailable =
          await shorebirdCodePush.isNewPatchAvailableForDownload();
      if (isUpdateAvailable) {
        // Show the update popup.
        AppWidget.showDialogPageSlide(context, const AlertUpdatePatchPage());
      }
    } catch (e) {
      print("❌ Error checking for updates: $e");
    }
  }

  @override
  void dispose() {
    homeTabController?.dispose();
    super.dispose();
  }

  bool showTutorial = false;
  int currentTutorialStep = 0;
  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (profileCtl.isLoading.value) {
        return AppLoader.loaderWaitPage(context);
      } else {
        return Scaffold(
          // floatingActionButton: FloatingActionButton(
          //   onPressed: (){
          //     aiTutorialCtl.dailyDashboard();
          //   },
          // ),
          extendBodyBehindAppBar: true,
          appBar: currentIndex < 3
              ? centerCtl.closeHead.value == false
                  ? AppBar(
                      automaticallyImplyLeading: false,
                      backgroundColor: Colors.transparent,
                      flexibleSpace: buildTopContainer(),
                      titleSpacing: 0,
                      elevation: 0,
                      toolbarHeight: 80,
                    )
                  : null
              : null,
          body: Stack(
            children: [
              Container(
                width: Get.width,
                height: Get.height,
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    stops: [0.0, 1.0],
                    colors: [
                      Color(0xFFEDEDED),
                      Color(0xFFF2F2F2),
                    ],
                  ),
                ),
              ),
              profileCtl.token.value != null
                  ? buildTabBarView()
                  : buildTabBarViewGuest(),
              profileCtl.token.value != null
                  ? buildFloatingButton()
                  : buildFloatingButtonGuest(),
              // if (!showTutorial)
              //   InkWell(
              //     onTap: () {
              //       setState(() {
              //         currentTutorialStep++; // เลื่อน step ก่อน startTutorial แล้วแต่ความต้องการ
              //         // startTutorial(); // เรียก startTutorial(); // เรียก
              //     child: Tutorial.buildTutorialOverlay(context, 3),
              //   )
              ///Ai Tutorial
              AiTutorial()
            ],
          ),
        );
      }
    });
  }

  void startTutorial() {
    setState(() {
      showTutorial = true;
      currentTutorialStep = 0;
    });
  }

  buildTopContainer() {
    return Stack(
      fit: StackFit.loose,
      children: [
        Obx(
          () => pageCtl.scrollParam.value >= 12
              ? ClipRRect(
                  child: BackdropFilter(
                    filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                    child: SizedBox(
                      width: Get.width,
                      height: 180,
                    ),
                  ),
                )
              : const SizedBox(),
        ),
        Container(
          width: Get.width,
          height: 120,
          margin: const EdgeInsets.only(
            top: 30,
            left: 18,
            right: 18,
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Row(
                  children: [
                    Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 62,
                          child: Image.asset(
                            'assets/image/home/<USER>',
                          ),
                        ),
                        Row(
                          children: [
                            GradientText(
                              'ISUZU',
                              style: const TextStyle(
                                fontSize: 8,
                                fontFamily: 'Prompt-Medium',
                                fontWeight: FontWeight.w700,
                                letterSpacing: 0.4,
                              ),
                              gradientDirection: GradientDirection.ttb,
                              colors: [
                                const Color(0xFF000000).withOpacity(0.7),
                                const Color(0xFF000000),
                              ],
                            ),
                            GradientText(
                              ' PRACHAKIJ',
                              style: const TextStyle(
                                fontSize: 8,
                                fontFamily: 'Prompt',
                                fontWeight: FontWeight.w500,
                                letterSpacing: 0.4,
                              ),
                              gradientDirection: GradientDirection.ttb,
                              colors: const [
                                Color(0xFF664701),
                                Color(0xFF1D1400),
                              ],
                            ),
                          ],
                        )
                      ],
                    ),
                    const SizedBox(
                      width: 4,
                    ),
                    // TODO :: LIKEWALLET
                    // InkWell(
                    //     hoverColor: Colors.transparent,
                    //     focusColor: Colors.transparent,
                    //     highlightColor: Colors.transparent,
                    //     splashColor: Colors.transparent,
                    //     onTap: (){
                    //       if(profileCtl.token.value != null){
                    //         AppWidget.showDialogPage(context, const MiniLikePage());
                    //       } else {
                    //         AppWidget.showDialogPageSlide(context, const LoginPage());
                    //       }
                    //     },
                    //     child: Row(
                    //       crossAxisAlignment: CrossAxisAlignment.center,
                    //       children: [
                    //         Image.asset('assets/image/home/<USER>', width: 16,),
                    //         Obx(() => profileCtl.token.value != null
                    //             ? likePointCtl.likePoint.value != 0.00
                    //             ? AppWidget.boldTextS(
                    //             context,
                    //             " ${AppService.numberFormat(likePointCtl.likePoint.value)}",
                    //             11,
                    //             const Color(0xFF895F00),
                    //             FontWeight.w500)
                    //             : AppWidget.boldTextS(
                    //             context,
                    //             " 0",
                    //             11,
                    //             const Color(0xFF895F00),
                    //             FontWeight.w500)
                    //             : AppWidget.boldTextS(
                    //             context,
                    //             " 0",
                    //             11,
                    //             const Color(0xFF895F00),
                    //             FontWeight.w500),),
                    //         AppWidget.boldTextS(
                    //             context,
                    //             " ไลค์",
                    //             10,
                    //             const Color(0xFF2B1710),
                    //             FontWeight.w500),
                    //       ],
                    //     )
                    // )
                    // TODO :: LIKEWALLET
                  ],
                ),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // TODO :: SCAN QR CODE
                  // profileCtl.token.value != null
                  //     ? InkWell(
                  //     hoverColor: Colors.transparent,
                  //     focusColor: Colors.transparent,
                  //     highlightColor: Colors.transparent,
                  //     splashColor: Colors.transparent,
                  //     onTap: () async {
                  //       var value = await Get.to(() => const GetQrCodePage());
                  //       if(value != null){
                  //         setState(() {
                  //           AppLoader.loader(context);
                  //         });
                  //         var responseEvent = await AppApi.getEventRegisterByID(context, value);
                  //         if(responseEvent["status"] == 200){
                  //           Get.to(() => DetailEventRegisterPage(responseEvent["result"]));
                  //         }
                  //       }
                  //     },
                  //     child: SvgPicture.asset("assets/image/home/<USER>"))
                  //     : Container(),
                  // TODO :: SCAN QR CODE
                  profileCtl.token.value != null
                      ? buildContainerChatInApp()
                      : Container(),
                  const SizedBox(
                    width: 8,
                  ),
                  profileCtl.token.value != null
                      ? buildContainerMR()
                      : InkWell(
                          hoverColor: Colors.transparent,
                          focusColor: Colors.transparent,
                          highlightColor: Colors.transparent,
                          splashColor: Colors.transparent,
                          onTap: () {
                            AppWidget.showDialogPageSlide(
                                context, const LoginPage());
                          },
                          child: Container(
                            width: 110,
                            height: 40,
                            decoration: BoxDecoration(
                              color: const Color(0xFFFFFFFF).withOpacity(0.7),
                              borderRadius: const BorderRadius.only(
                                topRight: Radius.circular(25),
                                topLeft: Radius.circular(25),
                                bottomRight: Radius.circular(25),
                                bottomLeft: Radius.circular(25),
                              ),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                AppWidget.boldTextS(context, "เข้าระบบ ", 13,
                                    const Color(0xFF895F00), FontWeight.w500),
                                Image.asset(
                                  'assets/image/home/<USER>',
                                  width: 30,
                                ),
                                const SizedBox(
                                  width: 5,
                                ),
                              ],
                            ),
                          ),
                        ),
                ],
              )
            ],
          ),
        ),
      ],
    );
  }

  //Tab bar
  buildTabBarView() {
    // ✅ ตรวจสอบว่า homeTabController สร้างแล้ว
    if (homeTabController == null) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    // ✅ สร้าง children list ตาม flexTap
    List<Widget> children = [];

    // ✅ เลื่อน Home tab เสมอ
    children.add(HomeMobile());

    // ✅ ถ้า login แล้ว และ รถ ให้  PSI tab
    if (profileCtl.token.value != null &&
        PsiCtl.carOwnerInfo.carList != null &&
        PsiCtl.carOwnerInfo.carList!.isNotEmpty) {
      children.add(PSIpage());
    }

    // ✅ เลื่อน News tab เสมอ
    children.add(HomeNews());

    // ✅ เลื่อน Promotion tab เสมอ
    children.add(HomePromotionPage());

    // ✅ ถ้า login แล้ว ให้ notification tab
    if (profileCtl.token.value != null) {
      children.add(NotificationListPage());
    }

    print(
        "📊 TabBarView children count: ${children.length}, flexTap: $flexTap");

    return TabBarView(
      physics: const NeverScrollableScrollPhysics(),
      controller: homeTabController,
      children: children,
    );
  }

  buildFloatingButton() {
    return Obx(() => Align(
          alignment: Alignment.bottomCenter,
          child: Container(
            margin: const EdgeInsets.only(bottom: 20),
            width: Get.width * 0.9,
            height: 59,
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                stops: [0.0, 0.5, 1.0],
                colors: [
                  Color(0xFFFFC700),
                  Color(0xFFFFB100),
                  Color(0xFFFF9900)
                ],
              ),
              borderRadius: BorderRadius.circular(20.0),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFF000000).withOpacity(0.1),
                  offset: const Offset(0, 3),
                  blurRadius: 3.0,
                ),
              ],
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // ✅ สร้าง tabs ตาม flexTap
                Expanded(
                  flex: flexTap,
                  child: TabBar(
                    controller: homeTabController,
                    indicatorColor: Colors.transparent,
                    dividerColor: Colors.transparent,
                    tabs: _buildTabs(),
                  ),
                )
              ],
            ),
          ),
        ));
  }

  // ✅ สร้าง method สร้าง tabs
  List<Tab> _buildTabs() {
    List<Tab> tabs = [];

    // ✅ Home tab - เลื่อนเสมอ
    tabs.add(Tab(
      child: currentIndex == 0
          ? SizedBox(
              height: 21,
              width: 21,
              child: Stack(
                children: [
                  Align(
                    alignment: Alignment.bottomLeft,
                    child: SvgPicture.asset(
                      'assets/image/home/<USER>',
                      color: Colors.white,
                      width: 19,
                    ),
                  ),
                ],
              ),
            )
          : SvgPicture.asset(
              'assets/image/home/<USER>',
              color: Colors.white.withOpacity(0.6),
              width: 19,
            ),
    ));

    int tabIndex = 1;

    // ✅ PSI tab (ถ้า login และ รถ)
    if (profileCtl.token.value != null &&
        PsiCtl.carOwnerInfo.carList != null &&
        PsiCtl.carOwnerInfo.carList!.isNotEmpty) {
      tabs.add(Tab(
        child: currentIndex == tabIndex
            ? SizedBox(
                height: 21,
                width: 21,
                child: Stack(
                  children: [
                    Align(
                      alignment: Alignment.bottomLeft,
                      child: SvgPicture.asset(
                        'assets/image/home/<USER>',
                        color: Colors.white,
                        width: 19,
                      ),
                    ),
                  ],
                ),
              )
            : SvgPicture.asset(
                'assets/image/home/<USER>',
                color: Colors.white.withOpacity(0.6),
                width: 19,
              ),
      ));
      tabIndex++;
    }

    // ✅ News tab - เลื่อนเสมอ
    tabs.add(Tab(
      child: currentIndex == tabIndex
          ? SizedBox(
              height: 21,
              width: 21,
              child: Stack(
                children: [
                  Align(
                    alignment: Alignment.bottomLeft,
                    child: SvgPicture.asset(
                      'assets/image/home/<USER>',
                      color: Colors.white,
                      width: 19,
                    ),
                  ),
                ],
              ),
            )
          : SvgPicture.asset(
              'assets/image/home/<USER>',
              color: Colors.white.withOpacity(0.6),
              width: 19,
            ),
    ));
    tabIndex++;

    // ✅ Promotion tab - เลื่อนเสมอ
    tabs.add(Tab(
      child: currentIndex == tabIndex
          ? SizedBox(
              height: 21,
              width: 21,
              child: Stack(
                children: [
                  Align(
                    alignment: Alignment.bottomLeft,
                    child: SvgPicture.asset(
                      'assets/image/home/<USER>',
                      color: Colors.white,
                      width: 19,
                    ),
                  ),
                ],
              ),
            )
          : SvgPicture.asset(
              'assets/image/home/<USER>',
              color: Colors.white.withOpacity(0.6),
              width: 19,
            ),
    ));
    tabIndex++;

    // ✅ Notification tab (ถ้า login แล้ว)
    if (profileCtl.token.value != null) {
      tabs.add(Tab(
        child: currentIndex == tabIndex
            ? SizedBox(
                height: 21,
                width: 21,
                child: Stack(
                  children: [
                    Align(
                      alignment: Alignment.bottomLeft,
                      child: SvgPicture.asset(
                        'assets/image/home/<USER>',
                        color: Colors.white,
                        width: 19,
                      ),
                    ),
                  ],
                ),
              )
            : SvgPicture.asset(
                'assets/image/home/<USER>',
                color: Colors.white.withOpacity(0.6),
                width: 19,
              ),
      ));
    }

    print("📊 Tabs count: ${tabs.length}, flexTap: $flexTap");
    return tabs;
  }

  //Tab bar Guest
  buildTabBarViewGuest() {
    // ✅ ตรวจสอบว่า homeTabController สร้างแล้ว
    if (homeTabController == null) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    return TabBarView(
      controller: homeTabController,
      children: const [
        HomeMobile(),
        HomeNews(),
        HomePromotionPage(),
      ],
    );
  }

  buildFloatingButtonGuest() {
    return Align(
      alignment: Alignment.bottomCenter,
      child: Container(
        margin: const EdgeInsets.only(bottom: 20),
        width: Get.width * 0.9,
        height: 59,
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            stops: [0.0, 0.5, 1.0],
            colors: [Color(0xFFFFC700), Color(0xFFFFB100), Color(0xFFFF9900)],
          ),
          borderRadius: BorderRadius.circular(20.0),
          boxShadow: [
            BoxShadow(
              color: const Color(0xFF000000).withOpacity(0.1),
              offset: const Offset(0, 3),
              blurRadius: 3.0,
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
              flex: 3, // ✅ Guest 3 tabs
              child: TabBar(
                controller: homeTabController,
                indicatorColor: Colors.transparent,
                dividerColor: Colors.transparent,
                indicator: const BoxDecoration(),
                padding: const EdgeInsets.only(left: 50, right: 50),
                tabs: [
                  Tab(
                    child: currentIndex == 0
                        ? SizedBox(
                            height: 21,
                            width: 21,
                            child: Stack(
                              children: [
                                Align(
                                  alignment: Alignment.bottomLeft,
                                  child: SvgPicture.asset(
                                    'assets/image/home/<USER>',
                                    color: Colors.white,
                                  ),
                                ),
                              ],
                            ),
                          )
                        : SvgPicture.asset(
                            'assets/image/home/<USER>',
                            color: Colors.white.withOpacity(0.6),
                          ),
                  ),
                  Tab(
                    child: currentIndex == 1
                        ? SizedBox(
                            height: 21,
                            width: 21,
                            child: Stack(
                              children: [
                                Align(
                                  alignment: Alignment.bottomLeft,
                                  child: SvgPicture.asset(
                                    'assets/image/home/<USER>',
                                    color: Colors.white,
                                  ),
                                ),
                              ],
                            ),
                          )
                        : SvgPicture.asset(
                            'assets/image/home/<USER>',
                            color: Colors.white.withOpacity(0.6),
                          ),
                  ),
                  Tab(
                    child: currentIndex == 2
                        ? SizedBox(
                            height: 21,
                            width: 21,
                            child: Stack(
                              children: [
                                Align(
                                  alignment: Alignment.bottomLeft,
                                  child: SvgPicture.asset(
                                    'assets/image/home/<USER>',
                                    color: Colors.white,
                                  ),
                                ),
                              ],
                            ),
                          )
                        : SvgPicture.asset(
                            'assets/image/home/<USER>',
                            color: Colors.white.withOpacity(0.6),
                          ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  //Tab bar Guest

  buildContainerMR() {
    ScrollController profileScrollController = ScrollController();
    if (profileCtl.profile.value.mrCode == null) {
      profileCtl.clearProfile();
      return;
    }
    if (profileCtl.rankMR.rankCurrent == "STANDARD") {
      return GestureDetector(
        onTap: () {
          Get.to(() => const ProfilePage());
          // profileCtl.showProfileTutorial(context, profileScrollController);
        },
        child: Container(
          width: 92,
          height: 40,
          decoration: const BoxDecoration(
            color: Color(0xFFAADEEA),
            borderRadius: BorderRadius.only(
              topRight: Radius.circular(25),
              topLeft: Radius.circular(25),
              bottomRight: Radius.circular(25),
              bottomLeft: Radius.circular(25),
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  AppWidget.boldTextS(context, "STANDARD", 8,
                      const Color(0xFF000000), FontWeight.w700),
                  AppWidget.boldTextS(context, profileCtl.profile.value.mrCode,
                      8, const Color(0xFF000000), FontWeight.w500),
                ],
              ),
              CircleAvatar(
                backgroundColor: Colors.transparent,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(50.0),
                  child: Image(
                    image: profileCtl.profile.value.profilePicture == null ||
                            profileCtl.profile.value.profilePicture == ""
                        ? const AssetImage('assets/image/home/<USER>')
                            as ImageProvider
                        : NetworkImage(
                            "${profileCtl.profile.value.profilePicture}"),
                    width: 28,
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    } else if (profileCtl.rankMR.rankCurrent == "PLUS") {
      return GestureDetector(
        onTap: () {
          Get.to(() => const ProfilePage());
        },
        child: Container(
          width: 92,
          height: 40,
          decoration: const BoxDecoration(
            color: Color(0xFFFFE100),
            borderRadius: BorderRadius.only(
              topRight: Radius.circular(25),
              topLeft: Radius.circular(25),
              bottomRight: Radius.circular(25),
              bottomLeft: Radius.circular(25),
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  AppWidget.boldTextS(context, "PLUS", 8,
                      const Color(0xFF000000), FontWeight.w700),
                  AppWidget.boldTextS(context, profileCtl.profile.value.mrCode,
                      8, const Color(0xFF000000), FontWeight.w500),
                ],
              ),
              CircleAvatar(
                backgroundColor: Colors.transparent,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(50.0),
                  child: Image(
                    image: profileCtl.profile.value.profilePicture == null ||
                            profileCtl.profile.value.profilePicture == ""
                        ? const AssetImage('assets/image/home/<USER>')
                            as ImageProvider
                        : NetworkImage(
                            "${profileCtl.profile.value.profilePicture}"),
                    width: 28,
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    } else if (profileCtl.rankMR.rankCurrent == "PRO") {
      return GestureDetector(
        onTap: () {
          Get.to(() => const ProfilePage());
        },
        child: Container(
          width: 92,
          height: 40,
          decoration: BoxDecoration(
            borderRadius: const BorderRadius.only(
              topRight: Radius.circular(25),
              topLeft: Radius.circular(25),
              bottomRight: Radius.circular(25),
              bottomLeft: Radius.circular(25),
            ),
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              stops: const [0.0, 1.0],
              colors: [
                const Color(0xFF000000).withOpacity(0.7),
                const Color(0xFF000000),
              ],
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  AppWidget.boldTextS(context, "PRO", 8,
                      const Color(0xFFFFFFFF), FontWeight.w700),
                  AppWidget.boldTextS(context, profileCtl.profile.value.mrCode,
                      8, const Color(0xFFFFFFFF), FontWeight.w500),
                ],
              ),
              CircleAvatar(
                backgroundColor: Colors.transparent,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(50.0),
                  child: Image(
                    image: profileCtl.profile.value.profilePicture == null ||
                            profileCtl.profile.value.profilePicture == ""
                        ? const AssetImage('assets/image/home/<USER>')
                            as ImageProvider
                        : NetworkImage(
                            "${profileCtl.profile.value.profilePicture}"),
                    width: 28,
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }
  }

  buildContainerChatInApp() {
    return InkWell(
      hoverColor: Colors.transparent,
      onTap: () async {
        if (chatInAppCtl.createGroup.isFalse) {
          var ckSubRegister = await chatInAppCtl.firstCheckTG();

          if (ckSubRegister == 'false') {
            await chatInAppCtl.sendOTPTG(context);
          } else if (ckSubRegister == "success") {
            Get.to(() => const WebViewTelegram());
            return;
          }
        }
      },
      child: Container(
        width: 92,
        height: 40,
        decoration: ShapeDecoration(
          gradient: const LinearGradient(
            begin: Alignment(0.00, -1.00),
            end: Alignment(0, 1),
            colors: [Color(0xFFFDFDFD), Color(0xFFFCFCFC), Color(0xFFF7F7F7)],
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            AppWidget.boldTextS(
                context, "แชท", 10, const Color(0xFF895F00), FontWeight.w500),
            const SizedBox(
              width: 5,
            ),
            Image.asset(
              'assets/icon/contact_icon.png',
              width: 30,
            ),
          ],
        ),
      ),
    );
  }
}
