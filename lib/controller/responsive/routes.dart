import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/controller/psiController/psi_controller.dart';

import 'package:mapp_prachakij_v3/controller/setting_controller/MR_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/login_controller/login_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/login_controller/register_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';

import 'package:mapp_prachakij_v3/view/home/<USER>/detail_plus_MR.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/detail_pro_MR.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/detail_standard_MR.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/main_new_MR.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>';
import 'package:mapp_prachakij_v3/view/home/<USER>/home_news.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/notification.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/home_promotion.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/accessory/accessory.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/appointment/appointment.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/broken_car/broken_car.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/home_service/booking_fix_home_sevice.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/home_service/home_service.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/insurance/insurance.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/pmg/pmg.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/sa/sa.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/talksc/talk_SC.dart';
import 'package:mapp_prachakij_v3/view/information_en.dart';
import 'package:mapp_prachakij_v3/view/login/login.dart';
import 'package:mapp_prachakij_v3/view/login/register.dart';
import 'package:mapp_prachakij_v3/view/login/verify.dart';
import 'package:mapp_prachakij_v3/view/profile/profile.dart';
import 'package:mapp_prachakij_v3/view/psi_page/myPSI.dart';
import 'package:mapp_prachakij_v3/view/tablet/home_news_tablet.dart';
import 'package:mapp_prachakij_v3/view/tablet/home_promotion_tablet.dart';
import 'package:mapp_prachakij_v3/view/error/not_found_page.dart';
import '../../index.dart';

class AppRoutes {
  static List<GetPage> get routes {
    List<GetPage> routeList = [
      /// Main app
      GetPage(
        name: '/',
        page: () => IndexPage(),
      ),
      GetPage(
        name: '/home',
        page: () => const HomeNavigator(),
      ),
      GetPage(
        name: '/login',
        page: () => const LoginPage(),
        binding: BindingsBuilder(() {
          Get.put(LoginController(), permanent: true);
          Get.put(ProfileController(), permanent: true);
        }),
      ),
      GetPage(
        name: '/register',
        page: () => const LoginPage(),
        binding: BindingsBuilder(() {
          Get.put(LoginController(), permanent: true);
          Get.put(RegisterController(), permanent: true);
        }),
      ),
      GetPage(
        name: '/otp',
        page: () => VerifyPage(),
        binding: BindingsBuilder(() {
          if (!Get.isRegistered<LoginController>()) {
            Get.put(LoginController(), permanent: true);
          }
        }),
        transition: kIsWeb ? Transition.noTransition : Transition.fadeIn,
      ),

      // ✅ เล่ม routes ของ services
      GetPage(
        name: '/appointment',
        page: () => const AppointmentPage(),
      ),
      GetPage(
        name: '/broken-car',
        page: () => const BrokenCarPage(),
      ),
      GetPage(
        name: '/home-service',
        page: () => const HomeServicePage(),
      ),
      GetPage(
        name: '/accessory',
        page: () => const AccessoryPage(),
      ),
      GetPage(
        name: '/sa',
        page: () => const SAPage(),
      ),
      GetPage(
        name: '/pmg',
        page: () => const PMGPage(),
      ),
      GetPage(
        name: '/insurance',
        page: () => const InsurancePage(),
      ),
      GetPage(
        name: '/talk-sc',
        page: () => const TalkSCPage(),
      ),
      GetPage(
        name: '/profile',
        page: () => const ProfilePage(),
      ),
      GetPage(
        name: '/notification',
        page: () => const NotificationListPage(),
      ),
      GetPage(
        name: '/news',
        page: () => const HomeNews(),
      ),
      GetPage(
        name: '/promotion',
        page: () => const HomePromotionPage(),
      ),

      // ✅ MR routes
      GetPage(
        name: '/mr-main',
        page: () => const mainNewMRPage(),
      ),
      GetPage(
        name: '/mr-standard',
        page: () => const DetailStandardMRPage(),
      ),
      GetPage(
        name: '/mr-plus',
        page: () => const DetailPlusMRPage(),
      ),
      GetPage(
        name: '/mr-pro',
        page: () => const DetailProMRPage(),
      ),
      GetPage(
        name: '/psi',
        page: () => const PSIpage(),
        binding: BindingsBuilder(() {
          Get.put(PsiController(), permanent: true);
        }),
      ),
    ];

    return routeList;
  }
}
