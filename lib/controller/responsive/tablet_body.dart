import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/api.dart';
import 'package:mapp_prachakij_v3/component/loader.dart';
import 'package:mapp_prachakij_v3/component/secureStorage.dart';
import 'package:mapp_prachakij_v3/component/widget.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/MR_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/chat_in_app_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/likepoint_controller/likepoint_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/login_controller/login_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/page_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/setting_controller.dart';
import 'package:mapp_prachakij_v3/main.dart';
import 'package:mapp_prachakij_v3/controller/service/service.dart';
import 'package:mapp_prachakij_v3/view/chat_in_app/webview_tg.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>';
import 'package:mapp_prachakij_v3/view/home/<USER>';
import 'package:mapp_prachakij_v3/view/home/<USER>/event_register/detail_event_register.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/notification.dart';
import 'package:mapp_prachakij_v3/view/login/login.dart';
import 'package:mapp_prachakij_v3/view/minilike/mini_like.dart';
import 'package:mapp_prachakij_v3/view/profile/profile.dart';
import 'package:mapp_prachakij_v3/view/tablet/home_news_tablet.dart';
import 'package:mapp_prachakij_v3/view/tablet/home_tablet.dart';
import 'package:mapp_prachakij_v3/view/tablet/home_promotion_tablet.dart';
import 'package:shorebird_code_push/shorebird_code_push.dart';
import 'package:simple_gradient_text/simple_gradient_text.dart';

class TabletBodyPage extends StatefulWidget {
  const TabletBodyPage({Key? key}) : super(key: key);

  @override
  State<TabletBodyPage> createState() => _TabletBodyPageState();
}

class _TabletBodyPageState extends State<TabletBodyPage>
    with TickerProviderStateMixin {
  TabController? homeTabController;
  int currentIndex = 0;
  bool agreementStatus = true;
  var count = 0.obs;

  final profileCtl = Get.put(ProfileController());
  final loginCtl = Get.put(LoginController());
  final pageCtl = Get.put(PageSelectController());
  final centerCtl = Get.put(SettingController());
  final likePointCtl = Get.put(LikePointController());
  final mrCtl = Get.put(ReferralMRController());
  final chatInAppCtl = Get.put(ChatInAppController()); // ✅ เ่่ม chatInAppCtl
  final shorebirdCodePush = ShorebirdCodePush();

  @override
  void initState() {
    super.initState();
    print("🚀 Initializing TabletBodyPage...");
    _initializeApp();
  }

  Future<void> _initializeApp() async {
    try {
      // ✅ ตรวจสอบ login status
      bool? loginStatus = await AppService.getPref('bool', 'loginStatus');
      print("🔐 Login status: $loginStatus");

      // ✅ กำหนดค่าเริ่มต้น tab length
      int tabLength = loginStatus == true ? 5 : 3;

      // ✅ สร้าง TabController
      homeTabController = TabController(vsync: this, length: tabLength);
      homeTabController!.addListener(_handleTabSelection);

      if (loginStatus == true) {
        // ✅ โหลด profile และ MR data
        await profileCtl.getProfileAndMR();
        print("✅ Profile and MR data loaded");

        // ✅ ปรับ tab length ตามการโหลด profile
        if (profileCtl.lengthTab.value != tabLength) {
          homeTabController?.dispose();
          homeTabController =
              TabController(vsync: this, length: profileCtl.lengthTab.value);
          homeTabController!.addListener(_handleTabSelection);
        }
      }

      // ✅ โหลดข้อมูลอื่นๆ
      centerCtl.getVersion(context);
      _checkForUpdates(context);

      print("✅ TabletBodyPage initialization completed");

      // ✅ Force update UI
      if (mounted) setState(() {});
    } catch (e) {
      print("❌ Error initializing TabletBodyPage: $e");
    }
  }

  void _handleTabSelection() {
    if (mounted) {
      setState(() {
        currentIndex = homeTabController?.index ?? 0;
      });
    }
  }

  @override
  void dispose() {
    homeTabController?.dispose();
    super.dispose();
  }

  Future<void> _checkForUpdates(BuildContext context) async {
    try {
      // Check whether a patch is available to install.
      final isUpdateAvailable =
          await shorebirdCodePush.isNewPatchAvailableForDownload();
      if (isUpdateAvailable) {
        // Show the update popup.
        AppWidget.showDialogPageSlide(context, const AlertUpdatePatchPage());
      }
    } catch (e) {
      print("❌ Error checking for updates: $e");
    }
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (profileCtl.isLoading.value) {
        return AppLoader.loaderWaitPage(context);
      } else {
        return Scaffold(
          extendBodyBehindAppBar: true,
          appBar: currentIndex < 3
              ? AppBar(
                  automaticallyImplyLeading: false,
                  backgroundColor: Colors.transparent,
                  flexibleSpace: buildTopContainer(),
                  titleSpacing: 0,
                  elevation: 0,
                  toolbarHeight: 90,
                )
              : null,
          body: Stack(
            children: [
              Container(
                width: Get.width,
                height: Get.height,
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    stops: [0.0, 1.0],
                    colors: [
                      Color(0xFFEDEDED),
                      Color(0xFFF2F2F2),
                    ],
                  ),
                ),
              ),
              profileCtl.token.value != null
                  ? buildTabBarView()
                  : buildTabBarViewGuest(),
              profileCtl.token.value != null
                  ? buildFloatingButton()
                  : buildFloatingButtonGuest(),
            ],
          ),
        );
      }
    });
  }

  buildTopContainer() {
    return Stack(
      fit: StackFit.loose,
      children: [
        Obx(
          () => pageCtl.scrollParam.value >= 12
              ? ClipRRect(
                  child: BackdropFilter(
                    filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                    child: SizedBox(
                      width: Get.width,
                      height: 180,
                    ),
                  ),
                )
              : const SizedBox(),
        ),
        Container(
          width: Get.width,
          height: 120,
          margin: const EdgeInsets.only(
            top: 30,
            left: 54,
            right: 54,
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Row(
                  children: [
                    Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 70,
                          child: Image.asset(
                            'assets/image/home/<USER>',
                          ),
                        ),
                        Row(
                          children: [
                            GradientText(
                              'ISUZU',
                              style: const TextStyle(
                                fontSize: 8,
                                fontFamily: 'Prompt-Medium',
                                fontWeight: FontWeight.w700,
                                letterSpacing: 0.4,
                              ),
                              gradientDirection: GradientDirection.ttb,
                              colors: [
                                const Color(0xFF000000).withOpacity(0.7),
                                const Color(0xFF000000),
                              ],
                            ),
                            GradientText(
                              ' PRACHAKIJ',
                              style: const TextStyle(
                                fontSize: 8,
                                fontFamily: 'Prompt',
                                fontWeight: FontWeight.w500,
                                letterSpacing: 0.4,
                              ),
                              gradientDirection: GradientDirection.ttb,
                              colors: const [
                                Color(0xFF664701),
                                Color(0xFF1D1400),
                              ],
                            ),
                          ],
                        )
                      ],
                    ),
                    const SizedBox(
                      width: 20,
                    ),
                    // TODO :: LIKEWALLET
                    // InkWell(
                    //     hoverColor: Colors.transparent,
                    //     focusColor: Colors.transparent,
                    //     highlightColor: Colors.transparent,
                    //     splashColor: Colors.transparent,
                    //     onTap: (){
                    //       if(profileCtl.token.value != null){
                    //         AppWidget.showDialogPage(context, const MiniLikePage());
                    //       } else {
                    //         AppWidget.showDialogPageSlide(context, const LoginPage());
                    //       }
                    //     },
                    //     child: Row(
                    //       crossAxisAlignment: CrossAxisAlignment.center,
                    //       children: [
                    //         Image.asset('assets/image/home/<USER>', width: 16,),
                    //         Obx(() => profileCtl.token.value != null
                    //             ? likePointCtl.likePoint.value != 0.00
                    //             ? AppWidget.boldTextS(
                    //             context,
                    //             " ${AppService.numberFormat(likePointCtl.likePoint.value)}",
                    //             12,
                    //             const Color(0xFF895F00),
                    //             FontWeight.w500)
                    //             : AppWidget.boldTextS(
                    //             context,
                    //             " 0",
                    //             12,
                    //             const Color(0xFF895F00),
                    //             FontWeight.w500)
                    //             : AppWidget.boldTextS(
                    //             context,
                    //             " 0",
                    //             12,
                    //             const Color(0xFF895F00),
                    //             FontWeight.w500),),
                    //         AppWidget.boldTextS(
                    //             context,
                    //             " ไลค์",
                    //             12,
                    //             const Color(0xFF2B1710),
                    //             FontWeight.w500),
                    //       ],
                    //     )
                    // )
                    // TODO :: LIKEWALLET
                  ],
                ),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // TODO :: SCAN QR CODE
                  // profileCtl.token.value != null
                  //     ? InkWell(
                  //     hoverColor: Colors.transparent,
                  //     focusColor: Colors.transparent,
                  //     highlightColor: Colors.transparent,
                  //     splashColor: Colors.transparent,
                  //     onTap: () async {
                  //       var value = await Get.to(() => const GetQrCodePage());
                  //       if(value != null){
                  //         setState(() {
                  //           AppLoader.loader(context);
                  //         });
                  //         var responseEvent = await AppApi.getEventRegisterByID(context, value);
                  //         if(responseEvent["status"] == 200){
                  //           Get.to(() => DetailEventRegisterPage(responseEvent["result"]));
                  //         }
                  //       }
                  //     },
                  //     child: SvgPicture.asset("assets/image/home/<USER>"))
                  //     : Container(),
                  // TODO :: SCAN QR CODE
                  profileCtl.token.value != null
                      ? buildContainerChatInApp()
                      : InkWell(
                          hoverColor: Colors.transparent,
                          focusColor: Colors.transparent,
                          highlightColor: Colors.transparent,
                          splashColor: Colors.transparent,
                          onTap: () {
                            AppWidget.showDialogPageSlide(
                                context, const LoginPage());
                          },
                          child: Container(
                            width: 110,
                            height: 40,
                            decoration: BoxDecoration(
                              color: const Color(0xFFFFFFFF).withOpacity(0.7),
                              borderRadius: const BorderRadius.only(
                                topRight: Radius.circular(25),
                                topLeft: Radius.circular(25),
                                bottomRight: Radius.circular(25),
                                bottomLeft: Radius.circular(25),
                              ),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                AppWidget.boldTextS(context, "เข้าระบบ ", 14,
                                    const Color(0xFF895F00), FontWeight.w500),
                                Image.asset('assets/image/home/<USER>',
                                    width: 30),
                                const SizedBox(
                                  width: 5,
                                ),
                              ],
                            ),
                          ),
                        ),
                ],
              )
            ],
          ),
        ),
      ],
    );
  }

  //Tab bar
  buildTabBarView() {
    // ✅ ตรวจสอบว่า homeTabController สร้างแล้ว
    if (homeTabController == null) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    return TabBarView(
      physics: const NeverScrollableScrollPhysics(),
      controller: homeTabController,
      children: const [
        HomeTablet(),
        HomeNewsTabletPage(),
        HomePromotionTabletPage(),
        NotificationListPage(),
        ProfilePage(),
      ],
    );
  }

  buildFloatingButton() {
    return Align(
      alignment: Alignment.bottomCenter,
      child: Container(
        margin: const EdgeInsets.only(
          bottom: 50,
        ),
        width: 500,
        height: 59,
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            stops: [0.0, 0.5, 1.0],
            colors: [Color(0xFFFFC700), Color(0xFFFFB100), Color(0xFFFF9900)],
          ),
          borderRadius: const BorderRadius.only(
            topRight: Radius.circular(20.0),
            topLeft: Radius.circular(20.0),
            bottomRight: Radius.circular(20.0),
            bottomLeft: Radius.circular(20.0),
          ),
          boxShadow: [
            BoxShadow(
              color: const Color(0xFF000000).withOpacity(0.1),
              offset: const Offset(0, 3),
              blurRadius: 3.0,
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
              flex: 5,
              child: TabBar(
                controller: homeTabController,
                indicatorColor: Colors.transparent,
                dividerColor: Colors.transparent,
                indicator: const BoxDecoration(),
                padding: const EdgeInsets.only(
                  left: 20,
                  right: 20,
                ),
                tabs: [
                  Tab(
                      child: currentIndex == 0
                          ? SizedBox(
                              height: 21,
                              width: 21,
                              child: Stack(
                                children: [
                                  Align(
                                    alignment: Alignment.bottomLeft,
                                    child: SvgPicture.asset(
                                        'assets/image/home/<USER>',
                                        color: Colors.white),
                                  ),
                                  Align(
                                    alignment: Alignment.topRight,
                                    child: Container(
                                      width: 8,
                                      height: 8,
                                      decoration: const BoxDecoration(
                                        borderRadius: BorderRadius.only(
                                          topLeft: Radius.circular(10),
                                          topRight: Radius.circular(10),
                                          bottomLeft: Radius.circular(10),
                                          bottomRight: Radius.circular(10),
                                        ),
                                        gradient: LinearGradient(
                                          begin: Alignment.topCenter,
                                          end: Alignment.bottomCenter,
                                          stops: [0, 1.0],
                                          colors: [
                                            Color(0xFF664701),
                                            Color(0xFF1D1400),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            )
                          : SvgPicture.asset(
                              'assets/image/home/<USER>',
                              color: Colors.white.withOpacity(0.6),
                            )),
                  Tab(
                      child: currentIndex == 1
                          ? SizedBox(
                              height: 21,
                              width: 21,
                              child: Stack(
                                children: [
                                  Align(
                                    alignment: Alignment.bottomLeft,
                                    child: SvgPicture.asset(
                                        'assets/image/home/<USER>',
                                        color: Colors.white),
                                  ),
                                  Align(
                                    alignment: Alignment.topRight,
                                    child: Container(
                                      width: 8,
                                      height: 8,
                                      decoration: const BoxDecoration(
                                        borderRadius: BorderRadius.only(
                                          topLeft: Radius.circular(10),
                                          topRight: Radius.circular(10),
                                          bottomLeft: Radius.circular(10),
                                          bottomRight: Radius.circular(10),
                                        ),
                                        gradient: LinearGradient(
                                          begin: Alignment.topCenter,
                                          end: Alignment.bottomCenter,
                                          stops: [0, 1.0],
                                          colors: [
                                            Color(0xFF664701),
                                            Color(0xFF1D1400),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            )
                          : SvgPicture.asset(
                              'assets/image/home/<USER>',
                              color: Colors.white.withOpacity(0.6),
                            )),
                  Tab(
                      child: currentIndex == 2
                          ? SizedBox(
                              height: 21,
                              width: 21,
                              child: Stack(
                                children: [
                                  Align(
                                      alignment: Alignment.bottomLeft,
                                      child: SvgPicture.asset(
                                        'assets/image/home/<USER>',
                                        color: Colors.white,
                                      )),
                                  Align(
                                    alignment: Alignment.topRight,
                                    child: Container(
                                      width: 8,
                                      height: 8,
                                      decoration: const BoxDecoration(
                                        borderRadius: BorderRadius.only(
                                          topLeft: Radius.circular(10),
                                          topRight: Radius.circular(10),
                                          bottomLeft: Radius.circular(10),
                                          bottomRight: Radius.circular(10),
                                        ),
                                        gradient: LinearGradient(
                                          begin: Alignment.topCenter,
                                          end: Alignment.bottomCenter,
                                          stops: [0, 1.0],
                                          colors: [
                                            Color(0xFF664701),
                                            Color(0xFF1D1400),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            )
                          : SvgPicture.asset(
                              'assets/image/home/<USER>',
                              color: Colors.white.withOpacity(0.6),
                            )),
                  Tab(
                      child: currentIndex == 3
                          ? SizedBox(
                              height: 21,
                              width: 21,
                              child: Stack(
                                children: [
                                  Align(
                                      alignment: Alignment.bottomLeft,
                                      child: SvgPicture.asset(
                                        'assets/image/home/<USER>',
                                        color: Colors.white,
                                      )),
                                  Align(
                                    alignment: Alignment.topRight,
                                    child: Container(
                                      width: 8,
                                      height: 8,
                                      decoration: const BoxDecoration(
                                        borderRadius: BorderRadius.only(
                                          topLeft: Radius.circular(10),
                                          topRight: Radius.circular(10),
                                          bottomLeft: Radius.circular(10),
                                          bottomRight: Radius.circular(10),
                                        ),
                                        gradient: LinearGradient(
                                          begin: Alignment.topCenter,
                                          end: Alignment.bottomCenter,
                                          stops: [0, 1.0],
                                          colors: [
                                            Color(0xFF664701),
                                            Color(0xFF1D1400),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            )
                          : SvgPicture.asset(
                              'assets/image/home/<USER>',
                              color: Colors.white.withOpacity(0.6),
                            )),
                  Tab(
                      child: currentIndex == 4
                          ? SizedBox(
                              height: 21,
                              width: 21,
                              child: Stack(
                                children: [
                                  Align(
                                      alignment: Alignment.bottomLeft,
                                      child: SvgPicture.asset(
                                        'assets/image/home/<USER>',
                                        color: Colors.white,
                                      )),
                                  Align(
                                    alignment: Alignment.topRight,
                                    child: Container(
                                      width: 8,
                                      height: 8,
                                      decoration: const BoxDecoration(
                                        borderRadius: BorderRadius.only(
                                          topLeft: Radius.circular(10),
                                          topRight: Radius.circular(10),
                                          bottomLeft: Radius.circular(10),
                                          bottomRight: Radius.circular(10),
                                        ),
                                        gradient: LinearGradient(
                                          begin: Alignment.topCenter,
                                          end: Alignment.bottomCenter,
                                          stops: [0, 1.0],
                                          colors: [
                                            Color(0xFF664701),
                                            Color(0xFF1D1400),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            )
                          : SvgPicture.asset(
                              'assets/image/home/<USER>',
                              color: Colors.white.withOpacity(0.6),
                            )),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  //Tab bar
  //Tab bar Guest
  buildTabBarViewGuest() {
    return TabBarView(
      controller: homeTabController,
      children: const [
        HomeTablet(),
        HomeNewsTabletPage(),
        HomePromotionTabletPage(),
      ],
    );
  }

  buildFloatingButtonGuest() {
    return Align(
      alignment: Alignment.bottomCenter,
      child: Container(
        margin: const EdgeInsets.only(
          bottom: 50,
        ),
        width: 500,
        height: 59,
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            stops: [0.0, 0.5, 1.0],
            colors: [Color(0xFFFFC700), Color(0xFFFFB100), Color(0xFFFF9900)],
          ),
          borderRadius: const BorderRadius.only(
            topRight: Radius.circular(20.0),
            topLeft: Radius.circular(20.0),
            bottomRight: Radius.circular(20.0),
            bottomLeft: Radius.circular(20.0),
          ),
          boxShadow: [
            BoxShadow(
              color: const Color(0xFF000000).withOpacity(0.1),
              offset: const Offset(0, 3),
              blurRadius: 3.0,
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
              flex: 5,
              child: TabBar(
                controller: homeTabController,
                indicatorColor: Colors.transparent,
                dividerColor: Colors.transparent,
                indicator: const BoxDecoration(),
                padding: const EdgeInsets.only(left: 80, right: 80),
                tabs: [
                  Tab(
                      child: currentIndex == 0
                          ? SizedBox(
                              height: 21,
                              width: 21,
                              child: Stack(
                                children: [
                                  Align(
                                    alignment: Alignment.bottomLeft,
                                    child: SvgPicture.asset(
                                        'assets/image/home/<USER>',
                                        color: Colors.white),
                                  ),
                                  Align(
                                    alignment: Alignment.topRight,
                                    child: Container(
                                      width: 8,
                                      height: 8,
                                      decoration: const BoxDecoration(
                                        borderRadius: BorderRadius.only(
                                          topLeft: Radius.circular(10),
                                          topRight: Radius.circular(10),
                                          bottomLeft: Radius.circular(10),
                                          bottomRight: Radius.circular(10),
                                        ),
                                        gradient: LinearGradient(
                                          begin: Alignment.topCenter,
                                          end: Alignment.bottomCenter,
                                          stops: [0, 1.0],
                                          colors: [
                                            Color(0xFF664701),
                                            Color(0xFF1D1400),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            )
                          : SvgPicture.asset(
                              'assets/image/home/<USER>',
                              color: Colors.white.withOpacity(0.6),
                            )),
                  Tab(
                      child: currentIndex == 1
                          ? SizedBox(
                              height: 21,
                              width: 21,
                              child: Stack(
                                children: [
                                  Align(
                                    alignment: Alignment.bottomLeft,
                                    child: SvgPicture.asset(
                                        'assets/image/home/<USER>',
                                        color: Colors.white),
                                  ),
                                  Align(
                                    alignment: Alignment.topRight,
                                    child: Container(
                                      width: 8,
                                      height: 8,
                                      decoration: const BoxDecoration(
                                        borderRadius: BorderRadius.only(
                                          topLeft: Radius.circular(10),
                                          topRight: Radius.circular(10),
                                          bottomLeft: Radius.circular(10),
                                          bottomRight: Radius.circular(10),
                                        ),
                                        gradient: LinearGradient(
                                          begin: Alignment.topCenter,
                                          end: Alignment.bottomCenter,
                                          stops: [0, 1.0],
                                          colors: [
                                            Color(0xFF664701),
                                            Color(0xFF1D1400),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            )
                          : SvgPicture.asset(
                              'assets/image/home/<USER>',
                              color: Colors.white.withOpacity(0.6),
                            )),
                  Tab(
                      child: currentIndex == 2
                          ? SizedBox(
                              height: 21,
                              width: 21,
                              child: Stack(
                                children: [
                                  Align(
                                      alignment: Alignment.bottomLeft,
                                      child: SvgPicture.asset(
                                        'assets/image/home/<USER>',
                                        color: Colors.white,
                                      )),
                                  Align(
                                    alignment: Alignment.topRight,
                                    child: Container(
                                      width: 8,
                                      height: 8,
                                      decoration: const BoxDecoration(
                                        borderRadius: BorderRadius.only(
                                          topLeft: Radius.circular(10),
                                          topRight: Radius.circular(10),
                                          bottomLeft: Radius.circular(10),
                                          bottomRight: Radius.circular(10),
                                        ),
                                        gradient: LinearGradient(
                                          begin: Alignment.topCenter,
                                          end: Alignment.bottomCenter,
                                          stops: [0, 1.0],
                                          colors: [
                                            Color(0xFF664701),
                                            Color(0xFF1D1400),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            )
                          : SvgPicture.asset(
                              'assets/image/home/<USER>',
                              color: Colors.white.withOpacity(0.6),
                            )),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
  //Tab bar Guest

  buildContainerMR() {
    if (profileCtl.profile.value.mrCode == null) {
      profileCtl.clearProfile();
      return;
    }
    if (profileCtl.rankMR.rankCurrent == "STANDARD") {
      return Container(
        width: 136,
        height: 50,
        decoration: const BoxDecoration(
          color: Color(0xFFAADEEA),
          borderRadius: BorderRadius.only(
            topRight: Radius.circular(25),
            topLeft: Radius.circular(25),
            bottomRight: Radius.circular(25),
            bottomLeft: Radius.circular(25),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                AppWidget.boldTextS(context, "STANDARD", 11,
                    const Color(0xFF000000), FontWeight.w700),
                AppWidget.boldTextS(context, profileCtl.profile.value.mrCode,
                    11, const Color(0xFF000000), FontWeight.w500),
              ],
            ),
            const SizedBox(
              width: 5,
            ),
            CircleAvatar(
              backgroundColor: Colors.transparent,
              // radius: ,
              child: ClipRRect(
                borderRadius: BorderRadius.circular(50.0),
                child: Image(
                  image: profileCtl.profile.value.profilePicture == null ||
                          profileCtl.profile.value.profilePicture == ""
                      ? const AssetImage('assets/image/home/<USER>')
                          as ImageProvider
                      : NetworkImage(
                          "${profileCtl.profile.value.profilePicture}"),
                  width: 34,
                ),
              ),
            ),
            const SizedBox(
              width: 5,
            ),
          ],
        ),
      );
    } else if (profileCtl.rankMR.rankCurrent == "PLUS") {
      return Container(
        width: 136,
        height: 50,
        decoration: const BoxDecoration(
          color: Color(0xFFFFE100),
          borderRadius: BorderRadius.only(
            topRight: Radius.circular(25),
            topLeft: Radius.circular(25),
            bottomRight: Radius.circular(25),
            bottomLeft: Radius.circular(25),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                AppWidget.boldTextS(context, "PLUS", 11,
                    const Color(0xFF000000), FontWeight.w700),
                AppWidget.boldTextS(context, profileCtl.profile.value.mrCode,
                    11, const Color(0xFF000000), FontWeight.w500),
              ],
            ),
            const SizedBox(
              width: 5,
            ),
            CircleAvatar(
              backgroundColor: Colors.transparent,
              radius: 14.0,
              child: ClipRRect(
                borderRadius: BorderRadius.circular(50.0),
                child: Image(
                  image: profileCtl.profile.value.profilePicture == null ||
                          profileCtl.profile.value.profilePicture == ""
                      ? const AssetImage('assets/image/home/<USER>')
                          as ImageProvider
                      : NetworkImage(
                          "${profileCtl.profile.value.profilePicture}"),
                  width: 34,
                ),
              ),
            ),
            const SizedBox(
              width: 5,
            ),
          ],
        ),
      );
    } else if (profileCtl.rankMR.rankCurrent == "PRO") {
      return Container(
        width: 136,
        height: 50,
        decoration: BoxDecoration(
          borderRadius: const BorderRadius.only(
            topRight: Radius.circular(25),
            topLeft: Radius.circular(25),
            bottomRight: Radius.circular(25),
            bottomLeft: Radius.circular(25),
          ),
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            stops: const [0.0, 1.0],
            colors: [
              const Color(0xFF000000).withOpacity(0.7),
              const Color(0xFF000000),
            ],
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                AppWidget.boldTextS(context, "PRO", 11, const Color(0xFFFFFFFF),
                    FontWeight.w700),
                AppWidget.boldTextS(context, profileCtl.profile.value.mrCode,
                    11, const Color(0xFFFFFFFF), FontWeight.w500),
              ],
            ),
            const SizedBox(
              width: 5,
            ),
            CircleAvatar(
              backgroundColor: Colors.transparent,
              child: ClipRRect(
                borderRadius: BorderRadius.circular(50.0),
                child: Image(
                  image: profileCtl.profile.value.profilePicture == null ||
                          profileCtl.profile.value.profilePicture == ""
                      ? const AssetImage('assets/image/home/<USER>')
                          as ImageProvider
                      : NetworkImage(
                          "${profileCtl.profile.value.profilePicture}"),
                  width: 34,
                ),
              ),
            ),
            const SizedBox(
              width: 5,
            ),
          ],
        ),
      );
    }
  }

  buildContainerChatInApp() {
    return InkWell(
      hoverColor: Colors.transparent,
      onTap: () async {
        if (chatInAppCtl.createGroup.isFalse) {
          var ckSubRegister = await chatInAppCtl.firstCheckTG();

          if (ckSubRegister == 'false') {
            await chatInAppCtl.sendOTPTG(context);
          } else if (ckSubRegister == "success") {
            Get.to(() => const WebViewTelegram());
            return;
          }
        }
      },
      child: Container(
        width: 92,
        height: 40,
        decoration: ShapeDecoration(
          gradient: const LinearGradient(
            begin: Alignment(0.00, -1.00),
            end: Alignment(0, 1),
            colors: [Color(0xFFFDFDFD), Color(0xFFFCFCFC), Color(0xFFF7F7F7)],
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            AppWidget.boldTextS(
                context, "แชท", 10, const Color(0xFF895F00), FontWeight.w500),
            const SizedBox(
              width: 5,
            ),
            Image.asset(
              'assets/icon/contact_icon.png',
              width: 30,
            ),
          ],
        ),
      ),
    );
  }
}
