import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/api.dart';
import 'package:mapp_prachakij_v3/component/firestore.dart';
import 'package:mapp_prachakij_v3/component/url.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/login_controller/login_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/login_controller/register_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';
import 'package:mapp_prachakij_v3/model/MR_model/MR.dart';
import 'package:mapp_prachakij_v3/model/MR_model/POI_MR.dart';
import 'package:mapp_prachakij_v3/model/MR_model/recommend_MR.dart';

import '../../view/home/<USER>/detail_plus_MR.dart';
import '../../view/home/<USER>/detail_pro_MR.dart';
import '../../view/home/<USER>/detail_standard_MR.dart';
import '../../view/home/<USER>/main_new_MR.dart';
import 'package:mapp_prachakij_v3/controller/tutorial_controller.dart';

class ReferralMRController extends GetxController {
  var isLoading = true.obs;
  ReferralList referralMR = ReferralList();

  ResponseRankMRFB resStandard = ResponseRankMRFB();
  ResponseRankMRFB resPlus = ResponseRankMRFB();
  ResponseRankMRFB resPro = ResponseRankMRFB();

  var status = true.obs;

  final profileCtl = Get.put(ProfileController());
  final registerClt = Get.put(RegisterController());
  final loginCtl = Get.put(LoginController());

  var loadingCheck = true.obs;
  var errorMessage = ''.obs; // ✅ ใช้ RxString เลือให้ Obx ปเดตค่า
  var usageCount = 0.obs;

  @override
  void onInit() async {
    super.onInit();
    await getReferralMR();
    isLoading.value = false;
    await getBenefit();
    loadingCheck.value = false;
    // getHistoryMrCode(profileCtl.profile.value.mrCode.toString());
    await getHistoryMrCode();
    update();
  }

  void showMRTutorial(BuildContext context) {
    final tutorialCtl = Get.find<TutorialController>();
    tutorialCtl.showTutorialPage(context, "mr");
  }

  Future<dynamic> getReferralMR() async {
    try {
      GetReferralMR valueMember = GetReferralMR.fromJson(
          {"MrCode": profileCtl.profile.value.mrCode, "takeOffset": 0});
      final responseReferralMR =
          await AppApi.post(AppUrl.getReferralMR, valueMember.toJson());
      if (responseReferralMR["status"] == 200) {
        referralMR = ReferralList.fromJson(responseReferralMR["result"]);
      } else if (responseReferralMR["status"] == 404) {
        referralMR = ReferralList(data: []);
      }
      update();
      // return status.value;
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
      const GetSnackBar(
        title: "ข้อพลาด",
        message: "ข้อพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<dynamic> getBenefit() async {
    try {
      resStandard = await FireStore.getRankMR('STANDARD');
      resPlus = await FireStore.getRankMR('PLUS');
      resPro = await FireStore.getRankMR('PRO');
      update();
      // return status.value;
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
      const GetSnackBar(
        title: "ข้อพลาด",
        message: "ข้อพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  navigatorMR() {
    try {
      if (profileCtl.profile.value.bookBankNoMR != "" &&
          profileCtl.profile.value.careerMR != "") {
        // ใช้ named route แทน Get.to()
        Get.toNamed('/mr-standard');
      } else {
        Get.toNamed('/mr-main');
      }
    } catch (e) {
      print('Navigation error in MR: $e');
      // Fallback navigation
      Get.to(() => const DetailStandardMRPage());
    }
  }

  checkMRCodeForRegisterBody(BuildContext context) async {
    try {
      loadingCheck.value = true;
      errorMessage.value = ''; // ✅ ล้างข้อความ Error ก่อนเริ่มโหลด
      // print("Check phone MR Code: ${profileCtl.profile.value.mobile}");
      update();

      String mrCodeInput =
          registerClt.refTextController.text.trim(); // ช่องว่างออก
      // print("MR Code: $mrCodeInput");

      // ✅ ตรวจสอบว่าเป็นเลขล้วน (ห้ามอักษร)
      if (!RegExp(r'^[0-9]+$').hasMatch(mrCodeInput)) {
        errorMessage.value = 'MR Code ต้องเป็นเลขเท่านั้น';
        loadingCheck.value = false;
        update();
        return;
      }

      Map<String, dynamic> data = {
        "mr_code": "MR-$mrCodeInput",
        // "phone_number": profileCtl.profile.value.mobile ?? "", // ✅ ใช้ค่า mobile จาก profileCtl
      };

      final response =
          await AppApi.callAPIjwt('POST', AppUrl.CheckAndUpdateMrCode, data);
      // print("Raw response: $response");

      if (response["status"] == 200) {
        var result = response["result"];
        if (result != null && result.isNotEmpty) {
          // print("responseIF: $result");
          errorMessage.value = ''; // ✅ ล้าง Error ถ้าข้อมูล
          Navigator.pop(context);
          await profileCtl.getProfileAndMR();
        } else {
          print("responseElse: No data found");
          errorMessage.value = 'แนะนำไม่ถูกต้อง ลองใหม่'; // ✅ แสดง Error
        }
      } else if (response["status"] == 400) {
        errorMessage.value = 'ไม่สามารถใช้แนะนำได้';
      } else if (response["status"] == 404) {
        errorMessage.value = 'แนะนำไม่ถูกต้อง ลองใหม่';
      } else if (response["status"] == 201) {
        errorMessage.value = 'ไม่สามารถใช้แนะนำของคุณได้';
      } else {
        errorMessage.value = 'ข้อผิดพลาด ลองใหม่';
      }
    } catch (e) {
      print("Exception occurred: $e");
      errorMessage.value = 'ข้อผิดพลาดในการเชื่อมต่อ';
    } finally {
      loadingCheck.value = false;
      update();
    }
  }

// หน้า mrcode
  checkMRCodeForMRPage(BuildContext context) async {
    try {
      loadingCheck.value = true;
      errorMessage.value = ''; // ✅ ล้างข้อความ Error ก่อนเริ่มโหลด
      // print("Check phone MR Code: ${profileCtl.profile.value.mobile}");
      update();

      String mrCodeInput =
          registerClt.refTextController.text.trim(); // ช่องว่างออก
      // print("MR Code: $mrCodeInput");

      // ✅ ตรวจสอบว่าเป็นเลขล้วน (ห้ามอักษร)
      if (!RegExp(r'^[0-9]+$').hasMatch(mrCodeInput)) {
        errorMessage.value = 'MR Code ต้องเป็นเลขเท่านั้น';
        loadingCheck.value = false;
        update();
        return;
      }

      Map<String, dynamic> data = {
        "mr_code": "MR-$mrCodeInput",
        "phone_number": profileCtl.profile.value.mobile ??
            "", // ✅ ใช้ค่า mobile จาก profileCtl
      };

      final response =
          await AppApi.callAPIjwt('POST', AppUrl.CheckAndUpdateMrCode, data);
      // print("Raw response: $response");

      if (response["status"] == 200) {
        var result = response["result"];
        if (result != null && result.isNotEmpty) {
          print("responseIF: $result");
          errorMessage.value = ''; // ✅ ล้าง Error ถ้าข้อมูล
          Navigator.pop(context);
          await profileCtl.getProfileAndMR();
        } else {
          print("responseElse: No data found");
          errorMessage.value = 'แนะนำไม่ถูกต้อง ลองใหม่'; // ✅ แสดง Error
        }
      } else if (response["status"] == 400) {
        errorMessage.value = 'ไม่สามารถใช้แนะนำได้';
      } else if (response["status"] == 404) {
        errorMessage.value = 'แนะนำไม่ถูกต้อง ลองใหม่';
      } else if (response["status"] == 201) {
        errorMessage.value = 'ไม่สามารถใช้แนะนำของคุณได้';
      } else {
        errorMessage.value = 'ข้อผิดพลาด ลองใหม่';
      }
    } catch (e) {
      print("Exception occurred: $e");
      errorMessage.value = 'ข้อผิดพลาดในการเชื่อมต่อ';
    } finally {
      loadingCheck.value = false;
      update();
    }
  }

  getHistoryMrCode() async {
    try {
      print("Get History MR Code");
      // print("MR Code: ${AppUrl.GetHistoryMrCode}");
      isLoading.value = true;
      update();

      Map<String, dynamic> data = {
        "mr_code": profileCtl.profile.value.mrCode,
        // "mr_code": "MR-039078",
      };
      // print("CodeMR: $data");

      final response =
          await AppApi.callAPIjwt('POST', AppUrl.GetHistoryMrCode, data);

      // print("Raw Response: $response"); // ✅ Fix here

      if (response["status"] == 200 && response.containsKey('result')) {
        usageCount.value = response['result']?["usage_count"] ?? 0;
      } else {
        usageCount.value = 0;
      }
    } catch (e) {
      print("Error: $e");
      usageCount.value = 0;
    } finally {
      isLoading.value = false;
      update();
    }
  }
}

class RecommendMRController extends GetxController {
  var isLoading = true.obs;
  var poiList = <ResponsePoi>[].obs;

  ResponseRecommend resPMG = ResponseRecommend();
  ResponseRecommend resPMGService = ResponseRecommend();
  ResponseRecommend resAccessory = ResponseRecommend();
  ResponseRecommend resInsurance = ResponseRecommend();

  @override
  void onInit() async {
    super.onInit();
    await getPOIMR();
    await getRecommend();
    // isLoading(false);
  }

  Future<dynamic> getPOIMR() async {
    try {
      final responsePOIMR = await AppApi.get(AppUrl.getPOImr);
      List<dynamic> poiListData = responsePOIMR["result"];
      poiList.assignAll(poiListData.map((poi) => ResponsePoi.fromJson(poi)));
      update();
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
      const GetSnackBar(
        title: "ข้อผิดพลาด",
        message: "ข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<dynamic> getRecommend() async {
    try {
      resPMG = await FireStore.getRecommend("งานย์ซ่อมและถัง");
      resPMGService = await FireStore.getRecommend("อู่เอ็ม เซอร์ส");
      resAccessory = await FireStore.getRecommend("ซื้ออะไหล่ ประยนต์ แม็คยาง");
      resInsurance = await FireStore.getRecommend("ทะเบียน");
    } catch (e) {
      print(e);
    }
  }
}
