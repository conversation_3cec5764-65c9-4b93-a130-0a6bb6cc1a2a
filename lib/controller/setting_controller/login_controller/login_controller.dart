import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_line_sdk/flutter_line_sdk.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/secureStorage.dart';
import 'package:mapp_prachakij_v3/controller/service/service.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/login_controller/register_controller.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';

import '../../../component/alert.dart';
import '../../../component/api.dart';
import '../../../component/loader.dart';
import '../../../component/url.dart';
import '../../../model/check_phone_exist.dart';
import '../../../model/generator_token.dart';
import '../../../model/login_apple.dart';
import '../../../model/login_line.dart';
import '../../../model/member.dart';
import '../../../model/send_code.dart';
import '../../../model/verify_code.dart';
import 'dart:html' as html;

import '../../tutorial_controller.dart';
import '../profile_controller.dart';
import 'agreement_controller.dart';

class LoginController extends GetxController {
  //new
  final TextEditingController phoneTextController = TextEditingController();
  final TextEditingController pinTextController = TextEditingController();
  //new
  final SecureStorage secureStorage = SecureStorage();
  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  RxString menu = "".obs;
  var isLoading = true.obs;
  ResponseCheckMember responseCheckMember = ResponseCheckMember();
  ResponseSendCode responseSendCode = ResponseSendCode();
  ResponseVerifyCode responseVerify = ResponseVerifyCode();
  ResponseGenToken responseGenToken = ResponseGenToken();
  ResponseSendOtp responseSendOTP = ResponseSendOtp();
  Line line = Line();
  Apple apple = Apple();
  final profileCtl = Get.put(ProfileController());
  final agreementCtl = Get.put(AgreementController());
  final tutorialCtl = Get.put(TutorialController());
  //new
  RxString typeMenu = "".obs;
  RxString page = "".obs;
  RxString showPhone = "".obs;
  //new

  @override
  void onInit() {
    super.onInit();

    if (kIsWeb) {
      _handleWebUrlOnInit();
    }
  }

  void _handleWebUrlOnInit() {
    final uri = Uri.parse(html.window.location.href);
    print("🌐 LoginController - Current URL: ${uri.toString()}");

    // ✅ ตรวจสอบ register URL with ref parameter
    if (uri.path.contains('/register')) {
      if (uri.queryParameters.containsKey('ref')) {
        String refCode = uri.queryParameters['ref'] ?? '';
        print("🔗 Found register URL with ref: $refCode");

        typeMenu.value = 'register';
        page.value = 'register';

        // ✅ ใช้ WidgetsBinding เอ่อรอให้ GetMaterialApp พร้อม
        WidgetsBinding.instance.addPostFrameCallback((_) {
          Future.delayed(Duration(milliseconds: 500), () {
            if (Get.isRegistered<RegisterController>()) {
              final registerCtl = Get.find<RegisterController>();
              registerCtl.setRefCodeFromUrl(refCode);
            }
          });
        });
      } else {
        typeMenu.value = 'register';
        page.value = 'register';
      }
      return;
    }

    // ✅ ตรวจสอบ login URL
    if (uri.path == '/login') {
      typeMenu.value = 'login';
      page.value = 'login';
      return;
    }
  }

  // ✅ Web-specific login success handling
  Future<void> handleLoginSuccess() async {
    try {
      print('Login success - saving status...');

      // Save login status
      await AppService.setPref('bool', 'loginStatus', true);

      // ✅ รอให้ระบบพร้อม
      await Future.delayed(Duration(milliseconds: 500));

      if (kIsWeb) {
        print('Web login success - cleaning up...');

        // ✅ Safe navigation
        await Future.delayed(Duration(milliseconds: 300));
        try {
          Get.offAllNamed('/home');
          print('✅ Web navigation to /home successful');
        } catch (e) {
          print('❌ Navigation error, using fallback: $e');
          // ✅ Fallback navigation
          html.window.location.href = '/home';
        }
      } else {
        // Mobile handling
        Get.offAllNamed('/home');
      }
    } catch (e) {
      print('Login success handling error: $e');
      // Fallback navigation
      if (kIsWeb) {
        html.window.location.href = '/home';
      } else {
        Get.offAllNamed('/home');
      }
    }
  }

  // ✅ OTP verification success
  Future<void> handleOTPSuccess() async {
    try {
      print('OTP verification successful');

      // Save login status first
      await AppService.setPref('bool', 'loginStatus', true);

      // ✅ รอให้ระบบพร้อม
      await Future.delayed(Duration(milliseconds: 500));

      if (kIsWeb) {
        print('Web OTP success - preparing navigation...');

        try {
          Get.offAllNamed('/home');
        } catch (e) {
          print('Navigation error, using fallback: $e');
          html.window.location.href = '/home';
        }
      } else {
        // Mobile handling
        Get.offAllNamed('/home');
      }
    } catch (e) {
      print('OTP success handling error: $e');

      // Fallback - force navigation
      if (kIsWeb) {
        html.window.location.href = '/home';
      } else {
        Get.offAllNamed('/home');
      }
    }
  }

  // ✅ Handle web navigation errors
  void handleWebNavigationError() {
    if (kIsWeb) {
      print('Web navigation error detected');

      // Clear all routes and start fresh
      Get.reset();
      Get.clearRouteTree();

      // Force navigation to home
      Future.delayed(Duration(milliseconds: 100), () {
        Get.offAllNamed('/home');
      });
    }
  }

  Future<dynamic> genToken(context, String userId, String roleId) async {
    try {
      isLoading(true);

      // Debug navigation state before token generation
      if (kDebugMode) {
        print('🔍 Token Generation - Get.context: ${Get.context != null ? "Available" : "NULL"}');
      }

      GenToken valueGenToken = GenToken.fromJson({
        "userId": userId,
        "appId": "pmsMobile",
        "roleId": roleId,
      });
      final resGenToken =
          await AppApi.post(AppUrl.generatorToken, valueGenToken.toJson());
      responseGenToken = ResponseGenToken.fromJson(resGenToken);
      var statusGen = resGenToken['status'];
      update();

      if (kDebugMode) {
        print('✅ Token generation successful, status: $statusGen');
      }

      return statusGen;
    } catch (e) {
      final errorMessage = e.toString();
      if (kDebugMode) {
        print('❌ Token generation error: $errorMessage');
      }

      // Handle specific navigation errors
      if (errorMessage.contains('contextless navigation') ||
          errorMessage.contains('GetMaterialApp') ||
          errorMessage.contains('Get.key')) {

        if (kDebugMode) {
          print('🔧 Attempting to fix contextless navigation error...');
        }

        // Wait a bit and try to recover
        Future.delayed(Duration(milliseconds: 1000), () {
          if (Get.context != null) {
            if (kDebugMode) {
              print('✅ Navigation context recovered');
            }
            try {
              Get.offAllNamed('/home');
            } catch (e) {
              if (kIsWeb) {
                html.window.location.href = '/home';
              }
            }
          } else {
            if (kDebugMode) {
              print('❌ Navigation context still not available, reloading...');
            }
            if (kIsWeb) {
              html.window.location.reload();
            }
          }
        });
      }

      const GetSnackBar(
        title: "ข้อพลาด",
        message: "ข้อพลาด",
        duration: Duration(seconds: 3),
      );
    } finally {
      isLoading(false);
    }
  }

  void saveTokenMessage(String phone) async {
    _firebaseMessaging.getToken().then((token) async {
      assert(token != null);
      Map data = {"phone": phone, "token": token};
      final response = await AppApi.post(AppUrl.saveNotifyToken, data);
      int statusCode = response["status"];
      if (statusCode == 200) {
        if (kDebugMode) {
          print("saveTokenMessage success");
        }
      } else {
        if (kDebugMode) {
          print('Unsuccessful save NotifyToken ');
        }
      }
    });
  }

  lineLogin(context) async {
    try {
      await LineSDK.instance.login(scopes: ["profile"]);
      final profile = await LineSDK.instance.getProfile();
      line = Line.fromJson(profile.data);
      var status = await loginWithLine(context, profile.userId);
      return status;
    } on PlatformException catch (e) {
      if (e.code != 'CANCEL') {
        AppAlert.showError(context, 'Error : lineLogin', 'ตกลง');
      }
    }
  }

  loginWithLine(context, lineID) async {
    try {
      AppLoader.loader(context);
      Map data = {"lineID": lineID};
      final response = await AppApi.post(AppUrl.checkMemberWithLine, data);
      int status = response["status"];
      if (status == 200) {
        var member = response['result'][0];
        var statusGenToken =
            await genToken(context, member['id'].toString(), member['roleId']);
        int statusToken = statusGenToken;
        if (statusToken == 200) {
          await AppService.setPref('bool', 'loginStatus', true);
          await secureStorage.writeSecureData(
              "userId", member['id'].toString());
          await secureStorage.writeSecureData(
              "accessToken", responseGenToken.accessToken.toString());
          await profileCtl.getProfileAndMR();
          saveTokenMessage(member['mobile']);

          // ✅ Web-specific navigation
          AppLoader.dismiss(context);
          if (kIsWeb) {
            await handleLoginSuccess();
          } else {
            Get.offAll(() => const HomeNavigator());
          }
        } else {
          AppLoader.dismiss(context);
          AppAlert.showNewAccept(context, 'เข้าระบบไม่สำเสร็จ', 'ต่อ', 'ตกลง');
        }
      } else {
        AppLoader.dismiss(context);
        menu.value = "registerSocial";
        return status;
      }
    } catch (e) {
      AppLoader.dismiss(context);
      AppAlert.showError(context, 'ERROR : loginWithLine', 'ตกลง');
    }
  }

  appleLogIn(context) async {
    try {
      if (kDebugMode) {
        print('join appleLogIn');
      }
      await SignInWithApple.isAvailable();
      final credential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
      );
      apple.userIdentifier = credential.userIdentifier;
      var status = await loginWithApple(context, credential.userIdentifier);
      return status;
    } catch (e) {
      AppService.sendError(context, e);
    }
  }

  loginWithApple(context, appleID) async {
    try {
      if (kDebugMode) {
        print('on loginWithApple');
      }
      AppLoader.loader(context);
      Map data = {"appleID": appleID};
      final response = await AppApi.post(AppUrl.checkMemberWithApple, data);
      int status = response["status"];
      if (status == 200) {
        var member = response['result'][0];
        Map getToken = {
          "userId": member['id'].toString(),
          "appId": "pmsMobile",
          "roleId": member['roleId']
        };
        final resToken = await AppApi.post(AppUrl.generatorToken, getToken);
        int statusToken = resToken['status'];
        if (statusToken == 200) {
          await AppService.setPref('bool', 'loginStatus', true);
          await secureStorage.writeSecureData(
              "userId", member['id'].toString());
          await secureStorage.writeSecureData(
              "accessToken", responseGenToken.accessToken.toString());
          await profileCtl.getProfileAndMR();
          saveTokenMessage(member['mobile']);

          // ✅ Web-specific navigation
          AppLoader.dismiss(context);
          if (kIsWeb) {
            await handleLoginSuccess();
          } else {
            Get.offAll(() => const HomeNavigator());
          }
        } else {
          AppLoader.dismiss(context);
          AppAlert.showNewAccept(context, 'เข้าระบบไม่สำเสร็จ', 'ต่อ', 'ตกลง');
        }
      } else {
        AppLoader.dismiss(context);
        menu.value = "registerSocial";
        return status;
      }
    } catch (e) {
      AppLoader.dismiss(context);
      AppService.sendError(context, 'login With Apple ไม่ได้!');
    }
  }

  //TODO :: ส่ง OTP
  int _retryCount = 0;
  newSendOTP(context) async {
    print("newSendOTP called");
    try {
      AppLoader.loader(context);
      if (phoneTextController.text.length != 10) {
        AppLoader.dismiss(context);
        return AppAlert.showNewAccept(
            context, "ตรวจสอบ", "เบอร์โทรของท่าน\nไม่ครบ 10 ตัว", "ตกลง");
      }
      String phone = "+66${phoneTextController.text.substring(1)}";
      Map sendCode = {
        "phone": phone,
        "from": "Prachakij",
      };
      print("Sending OTP request with data: $sendCode");
      final resSendOTP = await AppApi.post(AppUrl.requertOTP, sendCode);
      responseSendCode = ResponseSendCode.fromJson(resSendOTP);
      var status = resSendOTP["statusCode"];
      print("OTP response status: $status");

      // ✅ แก้ไข status check - 202 ก็ว่าสำเร็จ
      if (status == 200 || status == 202) {
        page.value = "verify";
        AppLoader.dismiss(context);
        return true;
      } else {
        AppLoader.dismiss(context);
        await AppAlert.showNewAccept(
            context, "ข้อผิดพลาด", "ลองใหม่ครั้ง", "ตกลง");
        return false;
      }
    } catch (e) {
      print("Error sending OTP: $e");
      AppLoader.dismiss(context);
      return false;
    }
  }

  Future<bool> newCheckOTP(BuildContext context) async {
    print("🔸 Executing newCheckOTP...");
    try {
      AppLoader.loader(context);

      String rawPhone =
          phoneTextController.text.replaceAll(RegExp(r'[^0-9]'), '');
      print("🔍 Raw phone: $rawPhone");

      if (rawPhone.length < 9) {
        AppLoader.dismiss(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text("กรอกเบอร์โทรให้ครบ 10 ตัว")),
        );
        return false;
      }

      String phoneCode = "+66${rawPhone.substring(1)}";
      String refCode = responseSendCode.refCode?.toString() ?? "";

      if (refCode.isEmpty) {
        AppLoader.dismiss(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text("ไม่พบรหัสอ้างอิง (refCode)")),
        );
        return false;
      }

      VerifyCode valueVerify = VerifyCode.fromJson({
        "phone": phoneCode,
        "otpCode": pinTextController.text,
        "refCode": refCode,
        "fromBU": "Prachakij",
      });

      final resVerify =
          await AppApi.post(AppUrl.verifyOTP, valueVerify.toJson());

      responseVerify = ResponseVerifyCode.fromJson(resVerify);
      var status = resVerify['statusCode'];

      if (status == 200) {
        print("🎉 OTP verification successful.");

        // ✅ สถานะการเข้าระบบ
        await AppService.setPref('bool', 'loginStatus', true);

        print("🔸 About to call newCheckProfile...");
        bool profileCheckSuccess = await newCheckProfile(context);
        print("🔸 Profile check result: $profileCheckSuccess");

        if (profileCheckSuccess) {
          print("✅ OTP and profile check completed successfully");
        } else {
          print("❌ Profile check failed");
          // Force dismiss loader if profile check failed
          try {
            AppLoader.dismiss(context);
            print('✅ AppLoader dismissed after profile check failure');
          } catch (e) {
            print('⚠️ AppLoader dismiss error: $e');
          }
        }

        // ✅ ไม่ต้อง handleOTPSuccess เพราะ newGenToken จะ navigation
        return profileCheckSuccess;
      } else {
        AppLoader.dismiss(context);
        String errorMessage = resVerify['message'] ?? 'ข้อผิดพลาดในการ OTP';
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(errorMessage)),
        );
        return false;
      }
    } catch (e) {
      print("🚨 Error during OTP check: ${e.toString()}");
      AppLoader.dismiss(context);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text("ข้อผิดพลาด ลองใหม่ครั้ง")),
      );
      return false;
    }
  }

  //TODO :: STEP 2 for login => check profile
  newCheckProfile(context) async {
    print('join newCheckProfile');
    try {
      CheckPhone valueCheckPhone = CheckPhone.fromJson({
        "phone": phoneTextController.text,
      });
      final responseCheckProfile =
          await AppApi.post(AppUrl.checkPhoneExist, valueCheckPhone.toJson());
      var status = responseCheckProfile['status'];
      if (status == 203) {
        print('🔸 Profile check successful, generating token...');
        responseCheckMember =
            ResponseCheckMember.fromJson(responseCheckProfile['result'][0]);

        print('🔸 About to call newGenToken...');
        await newGenToken(context, responseCheckMember.id.toString(),
            responseCheckMember.roleId.toString());

        print('🔸 newGenToken completed, checking home status...');
        tutorialCtl.checkHomeStatus();
        return true;
      } else if (status == 200) {
        AppLoader.dismiss(context);
        var res = await AppAlert.showNewAccept(
            context, "แจ้ง", "เบอร์โทรไม่เคยทำการ\nเพื่อใช้งาน", "ตกลง");
        if (res) {
          typeMenu.value = "register";
          page.value = "register";
        }
        return false;
      } else {
        AppLoader.dismiss(context);
        AppAlert.showNewAccept(context, "ข้อผิดพลาด", "ลองใหม่ครั้ง", "ตกลง");
        print('statusCheckPhone ไม่สำเร็จ');
        return false;
      }
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
      return false;
    }
  }

  //TODO :: STEP 3 for login => gen Token
  newGenToken(context, userId, roleId) async {
    try {
      print('🔸 Starting newGenToken with userId: $userId, roleId: $roleId');

      GenToken valueGenToken = GenToken.fromJson({
        "userId": userId,
        "appId": "pmsMobile",
        "roleId": roleId,
      });

      print('🔸 Calling token generation API...');
      final resGenToken =
          await AppApi.post(AppUrl.generatorToken, valueGenToken.toJson());
      responseGenToken = ResponseGenToken.fromJson(resGenToken);
      var statusGen = resGenToken['status'];

      print('🔸 Token generation response status: $statusGen');
      if (statusGen == 200) {
        print("✅ Token generated successfully");

        await AppService.setPref('bool', 'loginStatus', true);
        await secureStorage.writeSecureData("userId", userId);
        await secureStorage.writeSecureData(
            "accessToken", responseGenToken.accessToken.toString());

        // ✅ รอให้ระบบพร้อมก่อนโหลด profile
        await Future.delayed(Duration(milliseconds: 300));

        // โหลด profile
        try {
          await profileCtl.getProfileAndMR();
          print("✅ Profile loaded successfully");
        } catch (e) {
          print("⚠️ Profile loading error (continuing anyway): $e");
        }

        saveTokenMessage(responseCheckMember.mobile.toString());

        print('🔍 About to dismiss loader and navigate...');

        // ✅ Force dismiss all loaders
        try {
          AppLoader.forceDismissAll(context);
          print('✅ All loaders force dismissed');
        } catch (e) {
          print('⚠️ AppLoader dismiss error: $e');
        }

        isLoading.value = false;
        print('✅ isLoading set to false');

        // ✅ Navigation ไปหน้า Home
        print('🔍 Platform check - kIsWeb: $kIsWeb');
        if (kIsWeb) {
          print('🔸 Web login complete, navigating to home...');
          print('🔍 Current route before navigation: ${Get.currentRoute}');
          print('🔍 GetX context available: ${Get.context != null}');

          // รอให้ระบบพร้อมก่อน navigate (ไม่ใช้ Get.reset())
          await Future.delayed(Duration(milliseconds: 500));

          // Safe navigation with timeout
          try {
            print('🚀 Attempting navigation to /home...');
            Get.offAllNamed('/home');
            print('✅ Navigation to /home successful');

            // รอให้ navigation เสร็จ
            await Future.delayed(Duration(milliseconds: 500));
            print('🔍 Current route after navigation: ${Get.currentRoute}');

            // Force dismiss any remaining loaders
            try {
              AppLoader.forceDismissAll(context);
              print('✅ Final force dismiss after navigation');
            } catch (e) {
              print('⚠️ Final force dismiss error: $e');
            }

            // Timeout fallback - ถ้าหลัง 3 วินาทียังไม่ไปหน้า home
            Future.delayed(Duration(seconds: 3), () {
              if (Get.currentRoute != '/home') {
                print('⚠️ Navigation timeout - forcing browser redirect');
                html.window.location.href = '/home';
              }
            });

          } catch (e) {
            print('❌ Navigation error: $e');
            print('🔄 Trying fallback browser navigation...');

            // Force dismiss all loaders before fallback
            try {
              AppLoader.forceDismissAll(context);
            } catch (dismissError) {
              print('⚠️ Error dismissing before fallback: $dismissError');
            }

            // Fallback to browser navigation
            html.window.location.href = '/home';
          }
        } else {
          print('🔸 Mobile login complete, navigating to HomeNavigator...');
          Future.delayed(Duration.zero, () {
            Get.offAll(() => const HomeNavigator());
          });
        }

        print('🔍 End of newGenToken method');
      } else {
        AppLoader.dismiss(context);
        AppAlert.showNewAccept(context, "ข้อผิดพลาด",
            "สร้าง Token ไม่สำเร็จ\nลองใหม่ครั้ง", "ตกลง");
      }
    } catch (e) {
      print("❌ Token generation error: $e");
      AppLoader.dismiss(context);
      AppAlert.showNewAccept(context, "ข้อผิดพลาด",
          "ข้อผิดพลาดในการสร้าง Token\nลองใหม่ครั้ง", "ตกลง");
    }
  }

  // ✅ Logout with web handling
  Future<void> logout() async {
    try {
      await AppService.setPref('bool', 'loginStatus', false);
      await secureStorage.deleteSecureData("userId");
      await secureStorage.deleteSecureData("accessToken");

      // Clear text controllers
      phoneTextController.clear();
      pinTextController.clear();

      // Reset observables
      menu.value = "";
      typeMenu.value = "";
      page.value = "";

      if (kIsWeb) {
        // Clear everything for web
        Get.clearRouteTree();
        Get.reset();

        // Navigate to login
        Get.offAllNamed('/login');
      } else {
        Get.offAllNamed('/login');
      }
    } catch (e) {
      print('Logout error: $e');
    }
  }
}
