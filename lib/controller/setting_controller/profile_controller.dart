import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/MR_controller.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:html' as html show window;
import 'package:mapp_prachakij_v3/controller/setting_controller/refer_code_controller.dart';
import '../../component/api.dart';
import '../../component/secureStorage.dart';
import '../../component/url.dart';
import '../../model/MR_model/MR.dart';
import '../../model/member.dart';
import 'likepoint_controller/likepoint_controller.dart';
import 'login_controller/agreement_controller.dart';
import 'package:mapp_prachakij_v3/controller/tutorial_controller.dart';

class ProfileController extends GetxController
    with GetTickerProviderStateMixin {
  var profile = ResponseProfileAndMR().obs;
  var isLoading = true.obs;
  var token = Rxn<String>();
  var lengthTab = 3.obs;
  late TabController homeTabController;
  int currentIndex = 0;

  // ✅ เ่อม แปร rankMR
  ResponseRankMR rankMR = ResponseRankMR();

  final storage = GetStorage();
  final secureStorage = SecureStorage();
  final refCodeClt = Get.put(ReferCodeController());
  final agreementCtl = Get.put(AgreementController());

  @override
  void onInit() {
    super.onInit();
    homeTabController = TabController(vsync: this, length: lengthTab.value);
    homeTabController.addListener(_handleTabSelection);
  }

  void _handleTabSelection() {
    currentIndex = homeTabController.index;
    update();
  }

  @override
  void onClose() {
    homeTabController.dispose();
    super.onClose();
  }

  // ✅ Method ปเดต TabController เมื่อ lengthTab เปลี่ยน
  void updateTabController() {
    homeTabController.dispose();
    homeTabController = TabController(vsync: this, length: lengthTab.value);
    homeTabController.addListener(_handleTabSelection);
    update();
  }

  clearProfile() async {
    try {
      isLoading.value = true;
      profile.value = ResponseProfileAndMR();
      // ✅ เคลียร์ rankMR ด้วย
      rankMR = ResponseRankMR();
      secureStorage.deleteSecureData("userId");
      token.value = null;
      lengthTab.value = 3;
      homeTabController = TabController(vsync: this, length: lengthTab.value);
      homeTabController.addListener(_handleTabSelection);
      update();
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
      Get.snackbar(
        "ข้อพลาด",
        "ข้อพลาด",
        duration: const Duration(seconds: 3),
      );
    } finally {
      Future.delayed(const Duration(milliseconds: 1000), () {
        isLoading.value = false;
      });
    }
  }

  //
  // Future<dynamic> getProfileAndMR() async {
  //   try {
  //     print('เข้า getProfileAndMR');
  //     isLoading(true);
  //     var userID = await secureStorage.readSecureData("userId");
  //     if(userID == null){
  //       await clearProfile();
  //       return;
  //     }
  //
  //     token.value = await secureStorage.readSecureData("accessToken");
  //     // print("check token");
  //     // print(token.value);
  //     if(token.value == null){
  //       lengthTab.value = 3;
  //     } else {
  //       lengthTab.value = 5;
  //     }
  //     homeTabController = TabController(vsync: this, length: lengthTab.value);
  //     homeTabController.addListener(_handleTabSelection);
  //     // storage.read("phone_profile").then((value) {
  //     //   print(value);
  //     //   if(value != null && value != ""){
  //     //     profile.value.mobile = value;
  //     //   }
  //     // });
  //     Member valueMember = Member.fromJson({
  //       "id": userID,
  //       // "id": "7777",
  //     });
  //     final response = await AppApi.post(AppUrl.getProfileAndMR, valueMember.toJson());
  //     print(response['result']['ref_code_mr']);
  //     profile = ResponseProfileAndMR.fromJson(response['result']).obs;
  //     // likePointCtl.getLikePoint(profile.value.mobile);
  //     await getRankMR(profile.value.mrCode.toString());
  //     if(profile.value.acceptAgreement == "N" || profile.value.acceptAgreement == ""){
  //       agreementCtl.acceptAgreement(profile.value.id, profile.value.mobile);
  //     }
  //     storage.write("phone_profile", profile.value.mobile);
  //     refCodeClt.updateRefCodeMr();
  //     refCodeClt.getRefCode();
  //     // storage.write("phone_profile", "0898098146");
  //     update();
  //   } catch(e){
  //     if (kDebugMode) {
  //       print('พังตรง้');
  //       print(e);
  //     }
  //     const GetSnackBar(
  //       title: "ข้อพลาด",
  //       message: "ข้อพลาด",
  //       duration: Duration(seconds: 3),
  //     );
  //   } finally {
  //     Future.delayed(const Duration(milliseconds: 500), () {
  //       isLoading.value = false;
  //     });
  //   }
  // }
  Future<dynamic> getProfileAndMR() async {
    try {
      print('👤 เข้า getProfileAndMR');
      isLoading(true);

      var userID = await secureStorage.readSecureData("userId");
      if (userID == null) {
        print("❌ No userID found, clearing profile");
        await clearProfile();
        return;
      }

      token.value = await secureStorage.readSecureData("accessToken");
      print("🔑 Token status: ${token.value != null ? 'Found' : 'Not found'}");

      // ✅ ตั้งค่า tab length (ไม่ update TabController ที่นี่)
      lengthTab.value = token.value == null ? 3 : 5;
      print("📊 Tab length set to: ${lengthTab.value}");

      Member valueMember = Member.fromJson({"id": userID});
      final response =
          await AppApi.post(AppUrl.getProfileAndMR, valueMember.toJson());

      print("📡 API Response status: ${response['status']}");

      if (response['status'] == 200 &&
          response['result'] is Map<String, dynamic>) {
        profile = ResponseProfileAndMR.fromJson(response['result']).obs;
        print("✅ Profile loaded - Mobile: ${profile.value.mobile}");
        print("✅ Profile loaded - MR Code: ${profile.value.mrCode}");

        // ✅ เบอร์โทรใน storage ต่างๆ
        if (profile.value.mobile != null) {
          await _savePhoneToAllStorages(profile.value.mobile.toString());
        }

        // ✅ โหลด rank MR ได้ profile แล้ว
        if (profile.value.mrCode != null) {
          await getRankMR(profile.value.mrCode.toString());
        }

        if (profile.value.acceptAgreement == "N" ||
            profile.value.acceptAgreement == "") {
          agreementCtl.acceptAgreement(profile.value.id, profile.value.mobile);
        }

        storage.write("phone_profile", profile.value.mobile);
        refCodeClt.updateRefCodeMr();
        refCodeClt.getRefCode();

        print("✅ getProfileAndMR completed successfully");
      } else {
        print("❌ Invalid API response format or error");
      }
    } catch (e, stacktrace) {
      if (kDebugMode) {
        print('❌ Error in getProfileAndMR: $e');
        print('📍 Stacktrace: $stacktrace');
      }
      Get.snackbar(
        "ข้อพลาด",
        e.toString(),
        duration: const Duration(seconds: 3),
      );
    } finally {
      isLoading(false);
      update();
    }
  }

  Future<dynamic> getProfileEdit() async {
    try {
      isLoading(true);
      var userID = await secureStorage.readSecureData("userId");
      Member valueMember = Member.fromJson({
        "id": userID,
      });
      lengthTab.value = 5;
      // ✅ ไม่สร้าง TabController ที่นี่ ให้ MobileBodyPage จัดการ
      final response =
          await AppApi.post(AppUrl.getProfileAndMR, valueMember.toJson());
      profile = ResponseProfileAndMR.fromJson(response['result']).obs;

      // ✅ โหลด rank MR ได้ profile แล้ว
      if (profile.value.mrCode != null) {
        await getRankMR(profile.value.mrCode.toString());
      }

      update();
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
      Get.snackbar(
        "ข้อพลาด",
        "ข้อพลาด",
        duration: const Duration(seconds: 3),
      );
    } finally {
      Future.delayed(const Duration(milliseconds: 500), () {
        isLoading.value = false;
      });
    }
  }

  Future<dynamic> getRankMR(String idMR) async {
    try {
      print("🏆 Getting rank MR for: $idMR");

      GetRankMR valueGetRankMR = GetRankMR.fromJson({"MrCode": idMR});

      final responseRankMR =
          await AppApi.post(AppUrl.getRankReferralMR, valueGetRankMR.toJson());

      print("📊 Rank MR Response: ${responseRankMR['status']}");

      if (responseRankMR['status'] == 200 && responseRankMR['result'] != null) {
        rankMR = ResponseRankMR.fromJson(responseRankMR['result']);
        print(
            "✅ Rank MR loaded - Current: ${rankMR.rankCurrent}, Points: ${rankMR.pointLikeCurrent}");
      } else {
        print("❌ Invalid rank MR response");
        // ✅ ตั้งค่าเริ่มต้นถ้าไม่ได้ข้อมูล
        rankMR = ResponseRankMR(
          rankCurrent: "STANDARD",
          pointLikeCurrent: 0,
          pointLikeLastYear: 0,
        );
      }

      update();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error in getRankMR: $e');
      }

      // ✅ ตั้งค่าเริ่มต้นถ้า error
      rankMR = ResponseRankMR(
        rankCurrent: "STANDARD",
        pointLikeCurrent: 0,
        pointLikeLastYear: 0,
      );

      Get.snackbar(
        "ข้อพลาด",
        "ไม่สามารถโหลดข้อมูล MR ได้",
        duration: const Duration(seconds: 3),
      );

      update();
    }
  }

  Future<dynamic> saveProfile(id, firstname, lastname, idcard, birthday, number,
      moo, road, tumbol, amphur, province, zipcode, email, location) async {
    try {
      String? address = "";

      if (number != "") {
        address += number;
      }
      if (moo != "") {
        address += "  $moo";
      }
      if (road != "") {
        address += " ถนน $road";
      }
      if (tumbol != "") {
        address += " ตำบล $tumbol";
      }
      if (amphur != "") {
        address += " อำเภอ $amphur";
      }
      if (province != "") {
        address += "  $province";
      }
      if (zipcode != "") {
        address += " $zipcode";
      }

      SaveProfile valueSaveProfile = SaveProfile.fromJson({
        "id": id,
        "firstname": firstname,
        "lastname": lastname,
        "idcard": idcard,
        "birthday": birthday,
        "address": address,
        "number": number,
        "moo": moo,
        "road": road,
        "tumbol": tumbol,
        "amphur": amphur,
        "province": province,
        "zipcode": zipcode,
        "email": email,
        "location": location,
      });
      final response =
          await AppApi.post(AppUrl.saveProfileByID, valueSaveProfile.toJson());
      await getProfileAndMR();
      int status = response['status'];
      return status;
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
      Get.snackbar(
        "ข้อพลาด",
        "ข้อพลาด",
        duration: const Duration(seconds: 3),
      );
    } finally {
      isLoading(false);
    }
  }

  void showProfileTutorial(BuildContext context, ScrollController controller) {
    final tutorialCtl = Get.find<TutorialController>();

    // ตรวจสอบว่า controller ไม่เป็น null ก่อนส่งไป
    if (controller != null) {
      print("Sending ScrollController to TutorialController");
      tutorialCtl.showTutorialPage(context, "profile", controller: controller);
    } else {
      print("ScrollController is null in ProfileController");
      tutorialCtl.showTutorialPage(context, "profile");
    }
  }

  // ✅ เ่อม method เบอร์โทรใน storage หมด
  Future<void> _savePhoneToAllStorages(String phone) async {
    try {
      // SecureStorage
      await secureStorage.writeSecureData('phone', phone);

      // GetStorage
      await storage.write('phone', phone);

      // SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('phone', phone);

      // Web localStorage
      if (kIsWeb) {
        html.window.localStorage['phone'] = phone;
      }

      print("✅ Phone saved to all storages: $phone");
    } catch (e) {
      print("❌ Error saving phone to storages: $e");
    }
  }

  // ✅ เ่อม getter เช็คสถานะ MR
  bool get isMRActive =>
      profile.value.mrCode != null && profile.value.mrCode!.isNotEmpty;

  String get currentRank => rankMR.rankCurrent ?? "STANDARD";

  int get currentPoints => rankMR.pointLikeCurrent ?? 0;

  int get lastYearPoints => rankMR.pointLikeLastYear ?? 0;
}
