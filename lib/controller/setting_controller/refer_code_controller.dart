import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:mapp_prachakij_v3/component/api.dart';
import 'package:mapp_prachakij_v3/component/secureStorage.dart';
import 'package:mapp_prachakij_v3/component/url.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/login_controller/login_controller.dart';

class ReferCodeController extends GetxController {
  var storage = GetStorage();
  final SecureStorage secureStorage = SecureStorage();
  var isLoading = true.obs;

  updateRefCodeMr() async {
    try {
      print('🔸 Starting updateRefCodeMr');

      var usedPhone = await getLocalPhone();
      if (usedPhone == null || usedPhone.isEmpty) {
        print('❌ No phone number found for ref code update');
        return;
      }

      Map<String, dynamic> data = {"phone": usedPhone};

      print('🔸 UpdateRefCodeMr data: $data');

      // Add timeout and better error handling
      final response =
          await AppApi.callAPIjwt('POST', AppUrl.UpdateRefCode, data)
              .timeout(Duration(seconds: 10));

      print('🔸 UpdateRefCodeMr response: $response');

      if (response != null && response["status"] == 200) {
        print('✅ Update Ref Code Success');
      } else {
        print(
            '⚠️ Update Ref Code Failed: ${response?["message"] ?? "Unknown error"}');
        // Don't throw error - this is not critical for login flow
      }
    } catch (e) {
      print('⚠️ UpdateRefCodeMr error (non-critical): $e');
      // Don't throw error - this is not critical for login flow
    }
  }

  getRefCode() async {
    try {
      print('🔸 Starting getRefCode');

      var usedPhone = await getLocalPhone();
      if (usedPhone == null || usedPhone.isEmpty) {
        print('❌ No phone number found for get ref code');
        return;
      }

      Map<String, dynamic> data = {"phone": usedPhone};

      final response = await AppApi.callAPIjwt('POST', AppUrl.getRefCode, data)
          .timeout(Duration(seconds: 10));

      print('🔸 GetRefCode response: $response');

      if (response != null && response["status"] == 200) {
        print('✅ getRefCode Success');
        // Handle the response data here if needed
      } else {
        print(
            '⚠️ getRefCode Failed: ${response?["message"] ?? "Unknown error"}');
      }
    } catch (e) {
      print('⚠️ getRefCode error (non-critical): $e');
    }
  }

  Future<String?> getLocalPhone() async {
    try {
      // Try to get from storage first
      String? phone = storage.read("phone_profile");
      if (phone != null && phone.isNotEmpty) {
        return phone;
      }

      // Try to get from profile controller
      if (Get.isRegistered<ProfileController>()) {
        final profileCtl = Get.find<ProfileController>();
        if (profileCtl.profile.value.mobile != null &&
            profileCtl.profile.value.mobile!.isNotEmpty) {
          return profileCtl.profile.value.mobile;
        }
      }

      // Try to get from login controller
      if (Get.isRegistered<LoginController>()) {
        final loginCtl = Get.find<LoginController>();
        String rawPhone = loginCtl.phoneTextController.text.trim();
        if (rawPhone.isNotEmpty && rawPhone.length == 10) {
          return "+66${rawPhone.substring(1)}";
        }
      }

      return null;
    } catch (e) {
      print('Error getting local phone: $e');
      return null;
    }
  }
}
