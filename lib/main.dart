import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter_web_plugins/url_strategy.dart';
import 'package:mapp_prachakij_v3/controller/ai_tutorial_controller.dart';
import 'package:mapp_prachakij_v3/controller/psiController/psi_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/MR_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/chat_in_app_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/likepoint_controller/likepoint_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/login_controller/login_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/login_controller/register_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/page_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/setting_controller.dart';
import 'package:mapp_prachakij_v3/controller/tutorial_controller.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>';
import 'package:mapp_prachakij_v3/index.dart';
import 'package:mapp_prachakij_v3/view/error/not_found_page.dart';
import 'package:mapp_prachakij_v3/view/login/login.dart';
import 'package:mapp_prachakij_v3/view/login/register.dart';
import 'package:mapp_prachakij_v3/view/login/verify.dart';
import 'package:mapp_prachakij_v3/test_navigation.dart';
import 'package:mapp_prachakij_v3/config/environment.dart';
import 'firebase_options.dart';
import 'dart:html' as html show window;

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // ✅ Environment setup
  Environment.printEnvironmentInfo();

  if (kIsWeb) {
    print('🌐 Web mode detected - GetX navigation enabled');
  }

  // ✅ Global error handler with better recovery
  FlutterError.onError = (FlutterErrorDetails details) {
    print('❌ Flutter Error: ${details.exception}');
    print('❌ Stack trace: ${details.stack}');

    // ✅ Handle specific GetX errors
    final errorMessage = details.exception.toString();
    if (errorMessage.contains('contextless navigation') ||
        errorMessage.contains('GetMaterialApp') ||
        errorMessage.contains('Get.key') ||
        errorMessage.contains('Token generation')) {
      print('🔄 GetX navigation error detected');

      if (kIsWeb) {
        // Don't reload immediately, try to recover first
        Timer(const Duration(seconds: 2), () {
          try {
            // ไม่ใช้ Get.reset() เพราะจะทำให้ context หายไป
            print('🔄 Attempting navigation recovery...');

            // Force navigation to home
            if (Get.context != null) {
              Get.offAllNamed('/home');
            } else {
              // If no context, reload page
              print('❌ No GetX context available, reloading page');
              html.window.location.reload();
            }
          } catch (e) {
            print('Recovery failed, reloading: $e');
            html.window.location.reload();
          }
        });
      }
    }

    if (kDebugMode) {
      FlutterError.presentError(details);
    }
  };

  // ✅ Platform error handler with better recovery
  PlatformDispatcher.instance.onError = (error, stack) {
    print('❌ Platform Error: $error');
    print('❌ Stack trace: $stack');

    final errorMessage = error.toString();
    if (errorMessage.contains('Token generation') ||
        errorMessage.contains('contextless navigation') ||
        errorMessage.contains('GetMaterialApp') ||
        errorMessage.contains('SyntaxError') ||
        errorMessage.contains('Unexpected token')) {
      print('🔄 Critical error detected');

      if (kIsWeb) {
        Timer(const Duration(seconds: 1), () {
          try {
            // Clear any problematic state
            Get.reset();

            // Force clean reload
            final baseUrl = html.window.location.origin;
            html.window.location.replace(
                '$baseUrl/?clean=${DateTime.now().millisecondsSinceEpoch}');
          } catch (e) {
            html.window.location.reload();
          }
        });
      }
    }

    return true;
  };

  try {
    // Initialize Firebase
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    print('✅ Firebase initialized successfully');
  } catch (e) {
    print('❌ Firebase initialization error: $e');
  }

  // ✅ Initialize controllers with better error handling
  try {
    // Clear any existing instances first
    if (Get.isRegistered<ProfileController>()) {
      Get.delete<ProfileController>();
    }
    if (Get.isRegistered<LoginController>()) {
      Get.delete<LoginController>();
    }

    // Initialize fresh instances
    Get.put(ProfileController(), permanent: true);
    Get.put(LoginController(), permanent: true);
    Get.put(PageSelectController(), permanent: true);
    Get.put(SettingController(), permanent: true);
    Get.put(LikePointController(), permanent: true);
    Get.put(ReferralMRController(), permanent: true);
    Get.put(PsiController(), permanent: true);
    Get.put(TutorialController(), permanent: true);
    Get.put(ChatInAppController(), permanent: true);
    Get.put(AiTutorialController(), permanent: true);
    print('✅ All controllers initialized successfully');
  } catch (e) {
    print('❌ Controller initialization error: $e');

    if (kIsWeb) {
      Timer(const Duration(seconds: 2), () {
        final baseUrl = html.window.location.origin;
        html.window.location.replace(
            '$baseUrl/?reset=${DateTime.now().millisecondsSinceEpoch}');
      });
    }
  }

  // Web-specific setup
  if (kIsWeb) {
    usePathUrlStrategy();
    print('✅ Path URL strategy enabled');
  }

  runApp(MyApp());

  // Validate GetX setup after app starts
  if (kDebugMode) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Future.delayed(Duration(seconds: 2), () {
        try {
          // Simple GetX validation without NavigationDebug class
          print('🔍 GetX validation: ${Get.context != null ? "✅ Context OK" : "❌ No Context"}');
          print('🔍 GetX key: ${Get.key != null ? "✅ Key OK" : "❌ No Key"}');
          print('🔍 Current route: ${Get.currentRoute}');
        } catch (e) {
          print('🔍 GetX validation error: $e');
        }
      });
    });
  }
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      title: 'Prachakij',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        primarySwatch: Colors.blue,
        fontFamily: 'Prompt',
      ),
      // ✅ Essential GetX configuration with better error handling
      navigatorKey: Get.key,
      enableLog: Environment.enableDebugLogs,
      defaultTransition: Transition.fade,
      transitionDuration: const Duration(milliseconds: 300),

      // ✅ Enhanced error handling builder
      builder: (context, child) {
        ErrorWidget.builder = (FlutterErrorDetails errorDetails) {
          final errorMessage = errorDetails.exception.toString();

          if (errorMessage.contains('Token generation') ||
              errorMessage.contains('contextless navigation') ||
              errorMessage.contains('GetMaterialApp') ||
              errorMessage.contains('SyntaxError') ||
              errorMessage.contains('Unexpected token')) {
            print('🚨 Critical UI error detected: $errorMessage');

            if (kIsWeb) {
              Timer(const Duration(seconds: 1), () {
                try {
                  // ไม่ใช้ Get.reset() เพื่อรักษา GetX context
                  print('🔄 Attempting error recovery without resetting GetX...');
                  final baseUrl = html.window.location.origin;
                  html.window.location.replace(
                      '$baseUrl/?error_recovery=${DateTime.now().millisecondsSinceEpoch}');
                } catch (e) {
                  html.window.location.reload();
                }
              });
            }

            return Material(
              child: Container(
                color: const Color(0xFFFFB200),
                child: const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.refresh, size: 48, color: Colors.white),
                      SizedBox(height: 16),
                      Text(
                        'แก้ไข...',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      SizedBox(height: 8),
                      Text(
                        'ณารอ',
                        style: TextStyle(
                          color: Colors.white70,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          }

          return Material(
            child: Container(
              color: const Color(0xFFFFB200),
              child: const Center(
                child: Text(
                  'ข้อ พลาด',
                  style: TextStyle(color: Colors.white, fontSize: 18),
                ),
              ),
            ),
          );
        };

        return MediaQuery(
          data: MediaQuery.of(context).copyWith(textScaleFactor: 1.0),
          child: child ?? Container(),
        );
      },

      getPages: [
        GetPage(
          name: '/',
          page: () => const IndexPage(),
          transition: Transition.fade,
        ),
        GetPage(
          name: '/home',
          page: () => const IndexPage(),
          transition: Transition.fade,
        ),
        GetPage(
          name: '/login',
          page: () => const LoginPage(),
          transition: Transition.fade,
        ),
        GetPage(
          name: '/register',
          page: () => RegisterPage(),
          transition: Transition.fade,
          binding: BindingsBuilder(() {
            if (!Get.isRegistered<RegisterController>()) {
              Get.lazyPut(() => RegisterController());
            }
          }),
        ),
        GetPage(
          name: '/otp',
          page: () => const VerifyPage(),
          transition: Transition.fade,
        ),
        // Test navigation only in development
        if (Environment.enableTestNavigation)
          GetPage(
            name: '/test-navigation',
            page: () => NavigationTestPage(),
            transition: Transition.fade,
          ),
      ],
      initialRoute: '/',
      unknownRoute: GetPage(
        name: '/notfound',
        page: () => const IndexPage(),
        transition: Transition.fade,
      ),
    );
  }
}
