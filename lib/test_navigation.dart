import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class NavigationTestController extends GetxController {
  void testNavigation() {
    try {
      print('🧪 Testing GetX navigation...');
      print('🧪 Get.context: ${Get.context}');
      print('🧪 Get.key: ${Get.key}');
      print('🧪 Get.currentRoute: ${Get.currentRoute}');
      print('🧪 Get.routing.current: ${Get.routing.current}');
      
      if (Get.context != null) {
        print('✅ GetX context is available');
        Get.snackbar(
          'Navigation Test', 
          'GetX is working properly!',
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      } else {
        print('❌ GetX context is null');
        Get.snackbar(
          'Navigation Error', 
          'GetX context is not available',
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      print('❌ Navigation test error: $e');
    }
  }
  
  void testTokenGeneration() {
    try {
      print('🧪 Testing token generation context...');
      
      // Simulate token generation scenario
      if (Get.context != null) {
        print('✅ Context available for token generation');
        Get.snackbar(
          'Token Test', 
          'Context is ready for token generation',
          backgroundColor: Colors.blue,
          colorText: Colors.white,
        );
      } else {
        print('❌ No context for token generation');
        throw Exception('Token generation error: You are trying to use contextless navigation without a GetMaterialApp or Get.key.');
      }
    } catch (e) {
      print('❌ Token generation test error: $e');
      Get.snackbar(
        'Token Error', 
        e.toString(),
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }
}

class NavigationTestPage extends StatelessWidget {
  final NavigationTestController controller = Get.put(NavigationTestController());
  
  NavigationTestPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Navigation Test'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: EdgeInsets.all(20),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'GetX Navigation Test',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 30),
            
            ElevatedButton(
              onPressed: controller.testNavigation,
              child: Text('Test Navigation Context'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                minimumSize: Size(double.infinity, 50),
              ),
            ),
            
            SizedBox(height: 20),
            
            ElevatedButton(
              onPressed: controller.testTokenGeneration,
              child: Text('Test Token Generation Context'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                minimumSize: Size(double.infinity, 50),
              ),
            ),
            
            SizedBox(height: 20),
            
            ElevatedButton(
              onPressed: () {
                try {
                  Get.toNamed('/home');
                } catch (e) {
                  print('Navigation to home error: $e');
                  Get.snackbar('Error', 'Cannot navigate to home: $e');
                }
              },
              child: Text('Navigate to Home'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                minimumSize: Size(double.infinity, 50),
              ),
            ),
            
            SizedBox(height: 20),
            
            ElevatedButton(
              onPressed: () {
                try {
                  Get.back();
                } catch (e) {
                  print('Back navigation error: $e');
                  Get.snackbar('Error', 'Cannot go back: $e');
                }
              },
              child: Text('Go Back'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.grey,
                minimumSize: Size(double.infinity, 50),
              ),
            ),
            
            SizedBox(height: 30),
            
            Text(
              'Debug Info:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 10),
            Text('Platform: ${kIsWeb ? "Web" : "Mobile"}'),
            Text('Current Route: ${Get.currentRoute}'),
            Text('Context Available: ${Get.context != null}'),
          ],
        ),
      ),
    );
  }
}
