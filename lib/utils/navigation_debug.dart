import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'dart:html' as html show window;

class NavigationDebug {
  static void logNavigationState(String context) {
    if (kDebugMode) {
      print('🔍 Navigation Debug [$context]:');
      print('  - Get.context: ${Get.context != null ? "Available" : "NULL"}');
      print('  - Get.key: ${Get.key}');
      print('  - Get.currentRoute: ${Get.currentRoute}');
      print('  - Get.routing.current: ${Get.routing.current}');
      print('  - Get.routing.previous: ${Get.routing.previous}');
      print('  - Platform: ${kIsWeb ? "Web" : "Mobile"}');
      
      if (kIsWeb) {
        print('  - Browser URL: ${html.window.location.href}');
        print('  - Browser Path: ${html.window.location.pathname}');
      }
      print('  ----------------------------------------');
    }
  }
  
  static bool isNavigationReady() {
    final isReady = Get.context != null && Get.key != null;
    if (kDebugMode) {
      print('🔍 Navigation Ready Check: ${isReady ? "✅ READY" : "❌ NOT READY"}');
    }
    return isReady;
  }
  
  static void safeNavigate(String route, {
    Map<String, String>? parameters,
    dynamic arguments,
    bool offAll = false,
    String fallbackContext = 'Unknown'
  }) {
    try {
      logNavigationState('Before Navigation to $route');
      
      if (!isNavigationReady()) {
        throw Exception('GetX navigation not ready');
      }
      
      if (offAll) {
        Get.offAllNamed(route, parameters: parameters, arguments: arguments);
      } else {
        Get.toNamed(route, parameters: parameters, arguments: arguments);
      }
      
      if (kDebugMode) {
        print('✅ Navigation to $route successful');
      }
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ Navigation error in $fallbackContext: $e');
      }
      
      // Fallback for web
      if (kIsWeb) {
        try {
          final url = parameters != null && parameters.isNotEmpty
              ? '$route?${parameters.entries.map((e) => '${e.key}=${e.value}').join('&')}'
              : route;
          
          if (kDebugMode) {
            print('🔄 Using browser fallback navigation to: $url');
          }
          
          html.window.location.href = url;
        } catch (fallbackError) {
          if (kDebugMode) {
            print('❌ Fallback navigation also failed: $fallbackError');
          }
          // Last resort - reload page
          html.window.location.reload();
        }
      }
    }
  }
  
  static void handleTokenGenerationError(String errorMessage, String context) {
    if (kDebugMode) {
      print('🚨 Token Generation Error in $context:');
      print('  Error: $errorMessage');
    }
    
    logNavigationState('Token Generation Error');
    
    // Check if it's the specific contextless navigation error
    if (errorMessage.contains('contextless navigation') ||
        errorMessage.contains('GetMaterialApp') ||
        errorMessage.contains('Get.key')) {
      
      if (kDebugMode) {
        print('🔧 Attempting to fix contextless navigation error...');
      }
      
      // Wait a bit and try to recover
      Future.delayed(Duration(milliseconds: 1000), () {
        if (isNavigationReady()) {
          if (kDebugMode) {
            print('✅ Navigation context recovered');
          }
          safeNavigate('/home', offAll: true, fallbackContext: 'Token Error Recovery');
        } else {
          if (kDebugMode) {
            print('❌ Navigation context still not available, reloading...');
          }
          if (kIsWeb) {
            html.window.location.reload();
          }
        }
      });
    }
  }
  
  static void validateGetXSetup() {
    if (kDebugMode) {
      print('🔍 Validating GetX Setup:');
      
      // Check if GetMaterialApp is used
      final hasGetMaterialApp = Get.key != null;
      print('  - GetMaterialApp: ${hasGetMaterialApp ? "✅ Configured" : "❌ Missing"}');
      
      // Check if context is available
      final hasContext = Get.context != null;
      print('  - Context: ${hasContext ? "✅ Available" : "❌ Missing"}');
      
      // Check if routes are configured
      final hasRoutes = Get.routing.routes.isNotEmpty;
      print('  - Routes: ${hasRoutes ? "✅ Configured (${Get.routing.routes.length})" : "❌ Missing"}');
      
      // Overall status
      final isValid = hasGetMaterialApp && hasContext;
      print('  - Overall Status: ${isValid ? "✅ VALID" : "❌ INVALID"}');
      
      if (!isValid) {
        print('🚨 GetX setup is invalid! This will cause navigation errors.');
        print('💡 Make sure you are using GetMaterialApp instead of MaterialApp');
        print('💡 Make sure navigatorKey: Get.key is set');
      }
    }
  }
}
