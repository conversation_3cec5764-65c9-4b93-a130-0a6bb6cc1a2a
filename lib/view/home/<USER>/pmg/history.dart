import 'package:cached_network_image/cached_network_image.dart';
import 'package:colorful_safe_area/colorful_safe_area.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mapp_prachakij_v3/component/alert.dart';
import 'package:mapp_prachakij_v3/component/loader.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/chat_in_app_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/webview_tg_controller.dart';
import 'package:mapp_prachakij_v3/controller/sparePart/paymentTime_controller.dart';
import 'package:mapp_prachakij_v3/controller/sparePart/province_controller.dart';
import 'package:mapp_prachakij_v3/controller/sparePart/sparepart_Order_controller.dart';
import 'package:mapp_prachakij_v3/controller/sparePart/sparepart_controller.dart';
import 'package:mapp_prachakij_v3/model/sparePart/step_order.dart';

import 'package:mapp_prachakij_v3/view/home/<USER>/pmg/payment_state.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/pmg/tab_pmg.dart';


import 'package:url_launcher/url_launcher.dart';

class OrderHistory extends StatefulWidget {
  const OrderHistory({super.key});

  @override
  State<OrderHistory> createState() => _OrderHistoryState();
}

class _OrderHistoryState extends State<OrderHistory> {
  bool useTab = true;

  // bool swapPage = false;
  var formattedPrice =
      NumberFormat.currency(locale: 'en_US', symbol: '', decimalDigits: 0);

  final chatInAppCtl = Get.find<ChatInAppController>();
  final sparepartOrderCtl = Get.find<SparepartOrderController>();
  var ahlaiCtrl = Get.find<SparepartController>();
  final webviewCtl = Get.find<WebViewLikePointController>();

  var timeCtrl = Get.find<PaymentTimer>();
  List<bool> isFlipped = [];
  List<bool> usePMSPoint = [];

  // TextEditingController pmsPointCtrl = TextEditingController();
  List<TextEditingController> pmsPointCtrl = [];

  var countOrder = 0;
  var countPayment = 0;

  var interFaceText = {
    "ORDER_PLACED": {
      "icon": "assets/icon/sparePart/statusOrder/document.svg",
      "text": "สั่งซื้อสินค้า",
      "sub": "ทำการสั่งซื้อสำเร็จ รอเจ้าหน้าที่ติดต่อกลับภายใน 15-30 นาที",
    },
    "AWAITING_PAYMENT": {
      "icon": "assets/icon/sparePart/statusOrder/card-receive.svg",
      "text": "รอชำระเงิน",
      "sub": "สินค้าพร้อมจัดส่ง รอชำระเงิน",
    },
    "PAYMENT_COMPLETED": {
      "icon": "assets/icon/sparePart/statusOrder/card-check.svg",
      "text": "ชำระเงินเรียบร้อย",
      "sub": "ได้รับข้อมูลการชำระเงินของคุณเรียบร้อย",
    },
    "PREPARING_ORDER": {
      "icon": "assets/icon/sparePart/statusOrder/box.svg",
      "text": "จัดเตรียมสินค้า",
      "sub": "อยู่ในระหว่างจัดเตรียมสินค้า ใช้เวลา 1-3 วัน",
    },
    "READY_TO_SHIP": {
      "icon": "assets/icon/sparePart/statusOrder/check-circle.svg",
      "text": "สินค้าถูกจัดเตรียมเรียบร้อย",
      "sub": "เตรียมสินค้าเรียบร้อย พร้อมจัดส่ง",
    },
    "SHIPPED": {
      "icon": "assets/icon/sparePart/statusOrder/check-circle.svg",
      "text": "จัดส่งแล้ว",
      "sub": "สินค้าถูกจัดส่งแล้ว ภายใน 5-7 วัน",
    },
    "AWAITING_DELIVERY":{
      "icon": "assets/icon/sparePart/statusOrder/check-circle.svg",
      "text": "รอส่งมอบ",
      "sub": "สินค้ารอส่งมอบ",
    },
    "DELIVERED":{
      "icon": "assets/icon/sparePart/statusOrder/check-circle.svg",
      "text": "ส่งมอบแล้ว",
      "sub": "สินค้าถูกส่งมอบแล้ว",
    },
    "CANCELLED": {
      "icon": "assets/icon/sparePart/statusOrder/box_cancel.svg",
      "text": "ยกเลิกการสั่งซื้อ",
      "sub": "คำสั่งซื้อสินค้าของคุณ ถูกยกเลิก",
    }
  };

  List<StepOrder> stepProcess = [
    StepOrder(
      status: "ORDER_PLACED",
      icon: "assets/icon/sparePart/statusOrder/document.svg",
      text: "สั่งซื้อสินค้า",
      sub: "ทำการสั่งซื้อสำเร็จ รอเจ้าหน้าที่ติดต่อกลับภายใน 15-30 นาที",
    ),
    StepOrder(
      status: "AWAITING_PAYMENT",
      icon: "assets/icon/sparePart/statusOrder/card-receive.svg",
      text: "รอชำระเงิน",
      sub: "สินค้าพร้อมจัดส่ง รอชำระเงิน",
    ),
    StepOrder(
      status: "PAYMENT_COMPLETED",
      icon: "assets/icon/sparePart/statusOrder/card-check.svg",
      text: "ชำระเงินเรียบร้อย",
      sub: "ได้รับข้อมูลการชำระเงินของคุณเรียบร้อย",
    ),
    StepOrder(
      status: "PREPARING_ORDER",
      icon: "assets/icon/sparePart/statusOrder/box.svg",
      text: "จัดเตรียมสินค้า",
      sub: "อยู่ในระหว่างจัดเตรียมสินค้า ใช้เวลา 1-3 วัน",
    ),
    StepOrder(
      status: "READY_TO_SHIP",
      icon: "assets/icon/sparePart/statusOrder/check-circle.svg",
      text: "สินค้าถูกจัดเตรียมเรียบร้อย",
      sub: "เตรียมสินค้าเรียบร้อย พร้อมจัดส่ง",
    ),
    StepOrder(
      status: "SHIPPED",
      icon: "assets/icon/sparePart/statusOrder/check-circle.svg",
      text: "จัดส่งแล้ว",
      sub: "สินค้าถูกจัดส่งแล้ว ภายใน 5-7 วัน",
    ),
    StepOrder(
      status: "AWAITING_DELIVERY",
      icon: "assets/icon/sparePart/statusOrder/check-circle.svg",
      text: "รอส่งมอบ",
      sub: "สินค้ารอส่งมอบ",
    ),
    StepOrder(
      status: "DELIVERED",
      icon: "assets/icon/sparePart/statusOrder/check-circle.svg",
      text: "ส่งมอบแล้ว",
      sub: "สินค้าถูกส่งมอบแล้ว",
    ),
    StepOrder(
      status: "CANCELLED",
      icon: "assets/icon/sparePart/statusOrder/box_cancel.svg",
      text: "ยกเลิกการสั่งซื้อ",
      sub: "คำสั่งซื้อสินค้าของคุณ ถูกยกเลิก",
    ),
  ];

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    pmsPointCtrl = List.generate(
      sparepartOrderCtl.listOrder.length,
      (index) => TextEditingController(),
    );

    isFlipped =
        List.generate(sparepartOrderCtl.listOrder.length, (index) => false);

    // if (webviewCtl.balanceLikePointAvailable < 0) {
      usePMSPoint =
          List.generate(sparepartOrderCtl.listOrder.length, (index) => false);
    // } else {
    //   usePMSPoint =
    //       List.generate(sparepartOrderCtl.listOrder.length, (index) => true);
    // }

    timeCtrl.initializeTimer();
  }

  String formatThaiDate(String inputDate) {
    // Parse the input date string
    DateTime dateTime = DateTime.parse(inputDate);

    // Create a custom DateFormat for Thai date
    // Note that the year in Thai calendar is the current year + 543
    String formattedDate =
        DateFormat("d MMMM yyyy HH:mm", "th").format(dateTime);

    // Adjust the year for the Buddhist calendar (add 543 to the year)
    int buddhistYear = dateTime.year + 543;
    formattedDate =
        formattedDate.replaceAll("${dateTime.year}", "$buddhistYear");

    return formattedDate;
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
      },
      child: Scaffold(
        // floatingActionButton: FloatingActionButton(
        //   onPressed: () {
        //     final setImg = sparepartOrderCtl.listOrder;
        //     print(setImg);
        //   },
        //   child: const Icon(Icons.swap_horiz),
        // ),
        body: ColorfulSafeArea(
          color: Colors.black,
          child: GetBuilder<SparepartOrderController>(
              builder: (sparepartOrderCtl) {
            return Stack(
              children: [
                Container(
                  height: MediaQuery.of(context).size.height,
                  width: MediaQuery.of(context).size.width,
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment(0.00, -1.00),
                      end: Alignment(0, 1),
                      colors: [Color(0xFFEDEDED), Color(0xFFF2F2F2)],
                    ),
                  ),
                ),
                Column(
                  children: [
                    _buildAppBar(),
                    _buildTab(),
                    Expanded(
                      child: SingleChildScrollView(
                        child: _buildOrderList(),
                      ),
                    ),
                  ],
                )
              ],
            );
          }),
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return GetBuilder<SparepartController>(builder: (ahlaiCtrl) {
      return Container(
        width: MediaQuery.of(context).size.width,
        height: 50,
        color: Colors.white,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Container(
              width: MediaQuery.of(context).size.width * 0.2,
              margin: EdgeInsets.only(
                left: MediaQuery.of(context).size.width * 0.05,
              ),
              alignment: Alignment.centerLeft,
              child: InkWell(
                onTap: () {
                  Get.back();
                },
                child: Container(
                  width: 36,
                  height: 36,
                  decoration: BoxDecoration(
                      borderRadius: const BorderRadius.only(
                        topRight: Radius.circular(15),
                        topLeft: Radius.circular(15),
                        bottomRight: Radius.circular(15),
                        bottomLeft: Radius.circular(15),
                      ),
                      border:
                          Border.all(width: 1, color: const Color(0xFFE8E6E2))),
                  child: const Icon(
                    Icons.arrow_back_ios_new,
                    size: 18,
                    color: Color(0xFFFFB100),
                  ),
                ),
              ),
            ),
            const Text(
              "รายการสั่งซื้อสินค้า",
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Color(0xFF2B1710),
              ),
            ),
            Container(
              width: MediaQuery.of(context).size.width * 0.2,
              alignment: Alignment.centerRight,
              margin: EdgeInsets.only(
                right: MediaQuery.of(context).size.width * 0.05,
              ),
            )
          ],
        ),
      );
    });
  }

  Widget _buildTab() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        InkWell(
          onTap: () {
            setState(() {
              useTab = true;
            });
          },
          child: Stack(
            alignment: Alignment.topCenter,
            children: [
              Container(
                  width: MediaQuery.of(context).size.width * 0.5,
                  height: 50,
                  decoration: useTab
                      ? const BoxDecoration(
                          color: Color(0xFFFFB100),
                          borderRadius: BorderRadius.only(
                            bottomLeft: Radius.circular(20),
                          ),
                        )
                      : const BoxDecoration(
                          color: Colors.white,
                          border: Border(
                            right: BorderSide(
                              width: 1,
                              color: Color(0xFFBCBCBC),
                            ),
                          ),
                          borderRadius: BorderRadius.only(
                            bottomLeft: Radius.circular(18),
                          ),
                        )),
              useTab
                  ? Container(
                      width: MediaQuery.of(context).size.width * 0.5,
                      height: 47,
                      decoration: const BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.only(
                          bottomLeft: Radius.circular(18),
                        ),
                      ),
                    )
                  : Container(),
              Container(
                width: MediaQuery.of(context).size.width * 0.5,
                height: 50,
                alignment: Alignment.center,
                child: Text("คำสั่งซื้อ",
                    style: TextStyle(
                        fontSize: 14,
                        color:
                            useTab ? Colors.black : const Color(0x88282828))),
              ),
            ],
          ),
        ),
        InkWell(
          onTap: () {
            setState(() {
              useTab = false;
            });
          },
          child: Stack(
            alignment: Alignment.topCenter,
            children: [
              Container(
                  width: MediaQuery.of(context).size.width * 0.5,
                  height: 50,
                  decoration: !useTab
                      ? const BoxDecoration(
                          color: Color(0xFFFFB100),
                          borderRadius: BorderRadius.only(
                            bottomRight: Radius.circular(20),
                          ),
                        )
                      : const BoxDecoration(
                          color: Colors.white,
                          border: Border(
                            left: BorderSide(
                              width: 1,
                              color: Color(0xFFBCBCBC),
                            ),
                          ),
                          borderRadius: BorderRadius.only(
                            bottomRight: Radius.circular(18),
                          ),
                        )),
              !useTab
                  ? Container(
                      width: MediaQuery.of(context).size.width * 0.5,
                      height: 47,
                      decoration: const BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.only(
                          bottomRight: Radius.circular(18),
                        ),
                      ),
                    )
                  : Container(),
              Container(
                width: MediaQuery.of(context).size.width * 0.5,
                height: 50,
                alignment: Alignment.center,
                child: Text("การจัดส่ง",
                    style: TextStyle(
                        fontSize: 14,
                        color:
                            !useTab ? Colors.black : const Color(0x88282828))),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildOrderList() {
    return Obx(() => SizedBox(
          width: MediaQuery.of(context).size.width,
          child: sparepartOrderCtl.listOrder.isEmpty
              ? Container(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      const SizedBox(
                        height: 40,
                      ),
                      SizedBox(
                        width: MediaQuery.of(context).size.width * 0.3,
                        height: MediaQuery.of(context).size.width * 0.3,
                        child: Image.asset(
                            "assets/icon/sparePart/engineWithExclamation.png"),
                      ),
                      const SizedBox(
                        height: 20,
                      ),
                      const Text(
                        "ไม่มีข้อมูลคำสั่งซื้อ",
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: Color(0xFF895F00),
                        ),
                      ),
                      const Text(
                        "สนใจสั่งอะไหล่ และสินค้ากับประชากิจฯ\nสะดวกสบาย ง่ายๆ ได้แล้วันนี้",
                        maxLines: 2,
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 14,
                          color: Color(0xFF282828),
                        ),
                      ),
                      const SizedBox(
                        height: 30,
                      ),
                      InkWell(
                        onTap: () {
                          print("click");
                          Get.to(() => const TabPMG());
                          // Navigator.push(context, MaterialPageRoute(builder: (context) => const TabPMG()));
                          // sparepartOrderCtl.getSparepartOrder();
                        },
                        child: Container(
                          width: MediaQuery.of(context).size.width * 0.85,
                          height: 50,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(15),
                            gradient: const LinearGradient(
                              begin: Alignment(0.00, -1.00),
                              end: Alignment(0.00, 1.00),
                              colors: [Color(0xAA000000), Color(0xFF000000)],
                            ),
                          ),
                          alignment: Alignment.center,
                          child: const Text(
                            "สนใจสั่งสินค้า",
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.w500,
                              color: Color(0xFFFFB100),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                )
              : useTab
                  ? Column(
                      children: [
                        Builder(
                          builder: (context) {
                            countPayment = 0;
                            return Column(
                              children: List.generate(
                                  sparepartOrderCtl.listOrder.length, (index) {
                                try {
                                  if (sparepartOrderCtl.listOrder[index]
                                          ["status_order"] ==
                                      "AWAITING_PAYMENT") {
                                    return Column(
                                      children: [
                                        _makelistPayment(index),
                                        const SizedBox(
                                          height: 10,
                                        ),
                                      ],
                                    );
                                  }
                                  if (sparepartOrderCtl.listOrder[index]
                                          ["status_order"] ==
                                      "ORDER_PLACED") {
                                    return Column(
                                      children: [
                                        _makelistOrder(index),
                                        const SizedBox(
                                          height: 10,
                                        ),
                                      ],
                                    );
                                  }
                                } catch (e) {
                                  print(e);
                                  countPayment++;
                                  return Container();
                                }
                                countPayment++;
                                return Container();
                              }),
                            );
                          }
                        ),
                        if (countPayment == sparepartOrderCtl.listOrder.length)
                          Container(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                const SizedBox(
                                  height: 40,
                                ),
                                SizedBox(
                                  width:
                                      MediaQuery.of(context).size.width * 0.3,
                                  height:
                                      MediaQuery.of(context).size.width * 0.3,
                                  child: Image.asset(
                                      "assets/icon/sparePart/engineWithExclamation.png"),
                                ),
                                const SizedBox(
                                  height: 20,
                                ),
                                const Text(
                                  "ไม่มีข้อมูลคำสั่งซื้อ",
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w500,
                                    color: Color(0xFF895F00),
                                  ),
                                ),
                                const Text(
                                  "สนใจสั่งอะไหล่ และสินค้ากับประชากิจฯ\nสะดวกสบาย ง่ายๆ ได้แล้วันนี้",
                                  maxLines: 2,
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Color(0xFF282828),
                                  ),
                                ),
                                const SizedBox(
                                  height: 30,
                                ),
                                InkWell(
                                  onTap: () {
                                    print("click");
                                    Get.to(() => const TabPMG());
                                    // Navigator.push(context, MaterialPageRoute(builder: (context) => const TabPMG()));
                                    // sparepartOrderCtl.getSparepartOrder();
                                  },
                                  child: Container(
                                    width: MediaQuery.of(context).size.width *
                                        0.85,
                                    height: 50,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(15),
                                      gradient: const LinearGradient(
                                        begin: Alignment(0.00, -1.00),
                                        end: Alignment(0.00, 1.00),
                                        colors: [
                                          Color(0xAA000000),
                                          Color(0xFF000000)
                                        ],
                                      ),
                                    ),
                                    alignment: Alignment.center,
                                    child: const Text(
                                      "สนใจสั่งสินค้า",
                                      style: TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.w500,
                                        color: Color(0xFFFFB100),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          )
                      ],
                    )
                  : Column(
                      children: [
                        Builder(
                          builder: (context) {
                            countOrder = 0;
                            return Column(
                              children: List.generate(
                                  sparepartOrderCtl.listOrder.length, (index) {
                                try {
                                  if (sparepartOrderCtl.listOrder[index]
                                          ["status_order"] !=
                                      "AWAITING_PAYMENT" && sparepartOrderCtl.listOrder[index]["status_order"] !=
                                      "ORDER_PLACED") {
                                    return Column(
                                      children: [
                                        _makelistPayment(index),
                                        const SizedBox(
                                          height: 10,
                                        ),
                                      ],
                                    );
                                  }
                                } catch (e) {
                                  print(e);
                                  countOrder++;
                                  return Container();
                                }
                                countOrder++;
                                return Container();
                              }),
                            );
                          }
                        ),
                        if (countOrder == sparepartOrderCtl.listOrder.length)
                          Container(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                const SizedBox(
                                  height: 40,
                                ),
                                SizedBox(
                                  width:
                                      MediaQuery.of(context).size.width * 0.3,
                                  height:
                                      MediaQuery.of(context).size.width * 0.3,
                                  child: Image.asset(
                                      "assets/icon/sparePart/engineWithExclamation.png"),
                                ),
                                const SizedBox(
                                  height: 20,
                                ),
                                const Text(
                                  "ไม่มีข้อมูลคำสั่งซื้อ",
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w500,
                                    color: Color(0xFF895F00),
                                  ),
                                ),
                                const Text(
                                  "สนใจสั่งอะไหล่ และสินค้ากับประชากิจฯ\nสะดวกสบาย ง่ายๆ ได้แล้วันนี้",
                                  maxLines: 2,
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Color(0xFF282828),
                                  ),
                                ),
                                const SizedBox(
                                  height: 30,
                                ),
                                InkWell(
                                  onTap: () {
                                    print("click");
                                    // Get.to(() => const TabPMG());
                                    // Navigator.push(context, MaterialPageRoute(builder: (context) => const TabPMG()));
                                    sparepartOrderCtl.getSparepartOrder();
                                  },
                                  child: Container(
                                    width: MediaQuery.of(context).size.width *
                                        0.85,
                                    height: 50,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(15),
                                      gradient: const LinearGradient(
                                        begin: Alignment(0.00, -1.00),
                                        end: Alignment(0.00, 1.00),
                                        colors: [
                                          Color(0xAA000000),
                                          Color(0xFF000000)
                                        ],
                                      ),
                                    ),
                                    alignment: Alignment.center,
                                    child: const Text(
                                      "สนใจสั่งสินค้า",
                                      style: TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.w500,
                                        color: Color(0xFFFFB100),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          )
                      ],
                    ),
        ));
  }

  Widget _makelistOrder(mainIndex) {
    return GetBuilder<SparepartOrderController>(builder: (sparepartOrderCtl) {
      var totalPrice = sparepartOrderCtl.calculateTotalPriceWithShipping(
          sparepartOrderCtl.listOrder[mainIndex]["items"]);

      return Container(
        margin: EdgeInsets.all(MediaQuery.of(context).size.width * 0.05),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(15),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.2),
              spreadRadius: 1,
              blurRadius: 5,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: Container(
          margin: const EdgeInsets.only(
            left: 10,
            right: 10,
          ),
          child: Column(
            children: [
              Container(
                width: MediaQuery.of(context).size.width,
                margin: const EdgeInsets.only(
                  top: 10,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      "สถานะสินค้า",
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF2B1710),
                      ),
                    ),
                    InkWell(
                      onTap: () {
                        setState(() {
                          isFlipped[mainIndex] = !isFlipped[mainIndex];
                        });
                      },
                      child: Row(
                        children: [
                          Text(
                            "${interFaceText[sparepartOrderCtl.listOrder[mainIndex]["status_order"]!]?["text"]}",
                            style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: Color(0xFFFFB100),
                            ),
                          ),
                          const SizedBox(
                            width: 5,
                          ),
                          AnimatedRotation(
                            duration: const Duration(milliseconds: 1),
                            turns: isFlipped[mainIndex] ? 0.5 : 0,
                            child: const Icon(
                              Icons.expand_less,
                              color: Color(0xFF895F00),
                              size: 20,
                            ),
                          )
                        ],
                      ),
                    )
                  ],
                ),
              ),
              Container(
                width: MediaQuery.of(context).size.width,
                height: 20,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: List.generate(
                      (MediaQuery.of(context).size.width / 8).floor(), (index) {
                    return const Text(
                      "-",
                      style: TextStyle(fontSize: 12, color: Color(0xFFBCBCBC)),
                    );
                  }),
                ),
              ),
              AnimatedContainer(
                duration: const Duration(milliseconds: 1),
                height: isFlipped[mainIndex] ? null : 0,
                width: MediaQuery.of(context).size.width,
                child: buildStatusLine(
                    sparepartOrderCtl.listOrder[mainIndex]["status_order"],
                    mainIndex),
              ),
              AnimatedContainer(
                duration: const Duration(milliseconds: 1),
                width: MediaQuery.of(context).size.width,
                height: isFlipped[mainIndex] ? 20 : 0,
                child: isFlipped[mainIndex]
                    ? Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: List.generate(
                            (MediaQuery.of(context).size.width / 8).floor(),
                            (index) {
                          return const Text(
                            "-",
                            style: TextStyle(
                                fontSize: 12, color: Color(0xFFBCBCBC)),
                          );
                        }),
                      )
                    : Container(),
              ),
              Column(
                children: List.generate(
                    sparepartOrderCtl.listOrder[mainIndex]["items"].length,
                    (index) {
                  // final setImg = sparepartOrderCtl.showImg[mainIndex][index].toString().replaceAll("[", "").replaceAll("]", "");
                  return Container(
                    width: MediaQuery.of(context).size.width,
                    height: 100,
                    decoration: BoxDecoration(
                      // color: Colors.white,
                      borderRadius: BorderRadius.circular(15),
                    ),
                    child: Row(
                      children: [
                        Container(
                          width: 80,
                          height: 80,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(15),
                            gradient: const LinearGradient(
                              begin: Alignment(0.00, -1.00),
                              end: Alignment(0, 1),
                              colors: [Color(0xFFE8E6E2), Color(0xFFD9D8D5)],
                            ),
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(10),
                            child: CachedNetworkImage(
                              imageUrl: sparepartOrderCtl.listOrder[mainIndex]
                                      ["items"][index]["spare_parts_upload"]
                                  .toString(),
                              fit: BoxFit.cover,
                              // ปรับค่า BoxFit จาก BoxFit.fill เป็น BoxFit.cover
                              placeholder: (context, url) => const SizedBox(
                                width: 50,
                                height: 50,
                                child: Center(
                                  child: CircularProgressIndicator(
                                      color: Colors.orange),
                                ),
                              ),
                              errorWidget: (context, url, error) => Container(
                                width: 63,
                                height: 63,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(20),
                                  border: Border.all(
                                    width: 1,
                                    color: const Color(0xFFE8E6E2),
                                  ),
                                ),
                                child: SvgPicture.asset(
                                    "assets/icon/sparePart/no-image.svg"),
                              ),
                            ),
                          ),
                        ),
                        Container(
                          width: MediaQuery.of(context).size.width - 180,
                          height: 80,
                          padding: const EdgeInsets.only(left: 10),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "${sparepartOrderCtl.listOrder[mainIndex]["items"][index]["product_code"]}",
                                style: const TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                  color: Color(0xFF707070),
                                ),
                              ),
                              Text(
                                "จำนวน ${sparepartOrderCtl.listOrder[mainIndex]["items"][index]["quantity"]}",
                                style: const TextStyle(
                                  fontSize: 13,
                                  fontWeight: FontWeight.w400,
                                  color: Color(0xFF707070),
                                ),
                              ),
                              Text(
                                "฿ ${formattedPrice.format((double.parse(sparepartOrderCtl.listOrder[mainIndex]["items"][index]['price']!)) * (sparepartOrderCtl.listOrder[mainIndex]["items"][index]["quantity"] as num? ?? 0))}",
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w500,
                                  color: Color(0xFF895F00),
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(
                          width: 10,
                        ),
                      ],
                    ),
                  );
                }),
              ),
              Container(
                width: MediaQuery.of(context).size.width,
                height: 20,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: List.generate(
                      (MediaQuery.of(context).size.width / 8).floor(), (index) {
                    return const Text(
                      "-",
                      style: TextStyle(fontSize: 12, color: Color(0xFFBCBCBC)),
                    );
                  }),
                ),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                      "สินค้ารวม ${sparepartOrderCtl.listOrder[mainIndex]["items"].length} รายการ :",
                      style: const TextStyle(
                        fontSize: 13,
                        fontWeight: FontWeight.w500,
                        color: Color(0xFF2B1710),
                      )),
                  Text("฿ ${formattedPrice.format(totalPrice)}",
                      style: const TextStyle(
                        fontSize: 13,
                        fontWeight: FontWeight.w500,
                        color: Color(0xFF2B1710),
                      )),
                ],
              ),
              const SizedBox(
                height: 5,
              ),
              GetBuilder<ProvinceController>(
                builder: (provincCtrl) {
                  // Ensure address is fetched (you can call it in initState or similar)
                  provincCtrl.fetchAddressForOrder(
                      sparepartOrderCtl.listOrder[mainIndex]
                          ["shipping_address_id"],
                      mainIndex);

                  return Obx(() => Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text("ที่อยู่สำหรับจัดส่ง",
                              style: TextStyle(
                                fontSize: 13,
                                fontWeight: FontWeight.w500,
                                color: Color(0xFF2B1710),
                              )),
                          Row(
                            children: [
                              SvgPicture.asset(
                                  "assets/icon/sparePart/Location.svg"),
                              const SizedBox(width: 10),
                              SizedBox(
                                width: 130,
                                child: Text(
                                  provincCtrl.shippingAddress.isEmpty
                                      ? "กำลังโหลด..." // Show loading text if the list is empty
                                      : provincCtrl.shippingAddress[mainIndex],
                                  // Show the fetched address at the specific index
                                  overflow: TextOverflow.ellipsis,
                                  // Add ellipsis for overflow
                                  style: const TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w500,
                                    color: Color(0xFF707070),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ));
                },
              ),

              /// status == SHIPPED
              if (sparepartOrderCtl.listOrder[mainIndex]["status_order"] ==
                  "SHIPPED")
                Column(
                  children: [
                    const SizedBox(
                      height: 5,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text("หมายเลขพัสดุ",
                            style: TextStyle(
                              fontSize: 13,
                              fontWeight: FontWeight.w500,
                              color: Color(0xFF2B1710),
                            )),
                        Row(
                          children: [
                            Text(
                              sparepartOrderCtl.listOrder[mainIndex]["tracking_number"] ?? "ไม่มีข้อมูล",
                              // "TH034587F354",
                              style: const TextStyle(
                                fontSize: 13,
                                fontWeight: FontWeight.w500,
                                color: Color(0xFF2B1710),
                              ),
                            ),
                            SizedBox(
                              width: 10,
                            ),
                            InkWell(
                              onTap: () async {
                                var uri = Uri.parse(
                                    "https://th.kerryexpress.com/en/track/?track=TH034587F354");
                                await launchUrl(uri);
                              },
                              child: Container(
                                width: 50,
                                height: 20,
                                child: Center(
                                  child: Text(
                                    "ติดตาม",
                                    style: const TextStyle(
                                      fontSize: 13,
                                      fontWeight: FontWeight.bold,
                                      color: Color(0xFF895F00),
                                    ),
                                  ),
                                ),
                              ),
                            )
                          ],
                        )
                      ],
                    ),
                  ],
                ),
              Container(
                width: MediaQuery.of(context).size.width,
                height: 20,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: List.generate(
                      (MediaQuery.of(context).size.width / 8).floor(), (index) {
                    return const Text(
                      "-",
                      style: TextStyle(fontSize: 12, color: Color(0xFFBCBCBC)),
                    );
                  }),
                ),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  InkWell(
                    onTap: () async {
                      var uri = Uri.parse("https://lin.ee/CeJ928D");
                      await launchUrl(uri);
                    },
                    child: Container(
                        width: MediaQuery.of(context).size.width * 0.4,
                        height: 45,
                        decoration: BoxDecoration(
                          color: Colors.transparent,
                          borderRadius: BorderRadius.circular(10),
                          border: Border.all(
                            width: 1,
                            color: const Color(0xFFBCBCBC),
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Image.asset(
                              'assets/icon/contact_icon.png',
                              width: 30,
                            ),
                            const SizedBox(width: 8),
                            const Text(
                              'สอบถามเพิ่มเติม',
                              style: TextStyle(
                                  color: Color(0xFF282828), fontSize: 14),
                            ),
                          ],
                        )),
                  ),
                  sparepartOrderCtl.listOrder[mainIndex]["status_order"] ==
                          "CANCELLED" || sparepartOrderCtl.listOrder[mainIndex]["status_order"] == "DELIVERED" ||
      sparepartOrderCtl.listOrder[mainIndex]["status_order"] == "SHIPPED"
                      ? Container()
                      : InkWell(
                          onTap: () async {
                            var result = await AppAlert.showNewConfirm(
                                context,
                                'ยกเลิกคำสั่งซื้อ',
                                'คุณต้องการยกเลิกคำสั่งซื้อนี้ใช่หรือไม่?',
                                'ใช่',
                                'ไม่');
                            if (result) {
                              AppLoader.loader(BuildContext);
                              sparepartOrderCtl.cancelOrder(sparepartOrderCtl
                                  .listOrder[mainIndex]["order_id"]);

                              setState(() {});
                              AppLoader.dismiss(BuildContext);
                            }
                          },
                          child: Container(
                            width: MediaQuery.of(context).size.width * 0.4,
                            height: 45,
                            decoration: BoxDecoration(
                              color: const Color(0x0D282828),
                              borderRadius: BorderRadius.circular(10),
                            ),
                            alignment: Alignment.center,
                            child: const Text(
                              "ยกเลิกคำสั่งซื้อ",
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: Color(0xFF282828),
                              ),
                            ),
                          ),
                        )
                ],
              ),
              const SizedBox(
                height: 10,
              ),
            ],
          ),
        ),
      );
    });
  }

  Widget _makelistPayment(mainIndex) {
    return GetBuilder<SparepartOrderController>(builder: (sparepartOrderCtl) {
      var price = sparepartOrderCtl.calculateTotalPriceWithShipping(
          sparepartOrderCtl.listOrder[mainIndex]["items"]);
      var totalShipping = sparepartOrderCtl.calculateRateShipping(
          sparepartOrderCtl.listOrder[mainIndex]["items"]);

      if(sparepartOrderCtl.listOrder[mainIndex]["shipping_address_id"] == -1) {
        totalShipping = 0;
      }

      var totalPrice = price + totalShipping;
      return Container(
        margin: EdgeInsets.all(MediaQuery.of(context).size.width * 0.05),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(15),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.2),
              spreadRadius: 1,
              blurRadius: 5,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: Container(
          margin: const EdgeInsets.only(
            left: 10,
            right: 10,
          ),
          child: Column(
            children: [
              Container(
                width: MediaQuery.of(context).size.width,
                margin: const EdgeInsets.only(
                  top: 10,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      "สถานะสินค้า",
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF2B1710),
                      ),
                    ),
                    InkWell(
                      onTap: () {
                        setState(() {
                          isFlipped[mainIndex] = !isFlipped[mainIndex];
                        });
                      },
                      child: Row(
                        children: [
                          Text(
                            "${interFaceText[sparepartOrderCtl.listOrder[mainIndex]["status_order"]!]?["text"]}",
                            style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: Color(0xFFFFB100),
                            ),
                          ),
                          const SizedBox(
                            width: 5,
                          ),
                          AnimatedRotation(
                            duration: const Duration(milliseconds: 1),
                            turns: isFlipped[mainIndex] ? 0.5 : 0,
                            child: const Icon(
                              Icons.expand_less,
                              color: Color(0xFF895F00),
                              size: 20,
                            ),
                          )
                        ],
                      ),
                    )
                  ],
                ),
              ),
              SizedBox(
                width: MediaQuery.of(context).size.width,
                height: 20,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: List.generate(
                      (MediaQuery.of(context).size.width / 8).floor(), (index) {
                    return const Text(
                      "-",
                      style: TextStyle(fontSize: 12, color: Color(0xFFBCBCBC)),
                    );
                  }),
                ),
              ),
              AnimatedContainer(
                  duration: const Duration(milliseconds: 1),
                  height: isFlipped[mainIndex] ? null : 0,
                  width: MediaQuery.of(context).size.width,
                  child: buildStatusLine(
                      sparepartOrderCtl.listOrder[mainIndex]["status_order"],
                      mainIndex)

                  /// สถานะการจัดส่ง
                  ),
              AnimatedContainer(
                duration: const Duration(milliseconds: 1),
                width: MediaQuery.of(context).size.width,
                height: isFlipped[mainIndex] ? 20 : 0,
                child: isFlipped[mainIndex]
                    ? Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: List.generate(
                            (MediaQuery.of(context).size.width / 8).floor(),
                            (index) {
                          return const Text(
                            "-",
                            style: TextStyle(
                                fontSize: 12, color: Color(0xFFBCBCBC)),
                          );
                        }),
                      )
                    : Container(),
              ),
              Column(
                children: List.generate(
                    sparepartOrderCtl.listOrder[mainIndex]["items"].length,
                    (index) {
                  // final setImg = sparepartOrderCtl.showImg[0][index].toString().replaceAll("[", "").replaceAll("]", "");
                  return Container(
                    width: MediaQuery.of(context).size.width,
                    height: 100,
                    decoration: BoxDecoration(
                      // color: Colors.white,
                      borderRadius: BorderRadius.circular(15),
                    ),
                    child: Row(
                      children: [
                        Container(
                          width: 80,
                          height: 80,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(15),
                            gradient: const LinearGradient(
                              begin: Alignment(0.00, -1.00),
                              end: Alignment(0, 1),
                              colors: [Color(0xFFE8E6E2), Color(0xFFD9D8D5)],
                            ),
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(10),
                            child: CachedNetworkImage(
                              imageUrl: sparepartOrderCtl.listOrder[mainIndex]
                                      ["items"][index]["spare_parts_upload"]
                                  .toString(),
                              fit: BoxFit.cover,
                              // ปรับค่า BoxFit จาก BoxFit.fill เป็น BoxFit.cover
                              placeholder: (context, url) => const SizedBox(
                                width: 50,
                                height: 50,
                                child: Center(
                                  child: CircularProgressIndicator(
                                      color: Colors.orange),
                                ),
                              ),
                              errorWidget: (context, url, error) => Container(
                                width: 63,
                                height: 63,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(20),
                                  border: Border.all(
                                    width: 1,
                                    color: const Color(0xFFE8E6E2),
                                  ),
                                ),
                                child: SvgPicture.asset(
                                    "assets/icon/sparePart/no-image.svg"),
                              ),
                            ),
                          ),
                        ),
                        Container(
                          width: MediaQuery.of(context).size.width - 180,
                          height: 80,
                          padding: const EdgeInsets.only(left: 10),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "${sparepartOrderCtl.listOrder[mainIndex]["items"][index]["product_code"]}",
                                style: const TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                  color: Color(0xFF707070),
                                ),
                              ),
                              Text(
                                "จำนวน ${sparepartOrderCtl.listOrder[mainIndex]["items"][index]["quantity"]}",
                                style: const TextStyle(
                                  fontSize: 13,
                                  fontWeight: FontWeight.w400,
                                  color: Color(0xFF707070),
                                ),
                              ),
                              Text(
                                "฿ ${formattedPrice.format((double.parse(sparepartOrderCtl.listOrder[mainIndex]["items"][index]['price']!)) * (sparepartOrderCtl.listOrder[mainIndex]["items"][index]["quantity"] as num? ?? 0))}",
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w500,
                                  color: Color(0xFF895F00),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  );
                }),
              ),
              SizedBox(
                width: MediaQuery.of(context).size.width,
                height: 20,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: List.generate(
                      (MediaQuery.of(context).size.width / 8).floor(), (index) {
                    return const Text(
                      "-",
                      style: TextStyle(fontSize: 12, color: Color(0xFFBCBCBC)),
                    );
                  }),
                ),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                      "สินค้ารวม ${sparepartOrderCtl.listOrder[mainIndex]["items"].length} รายการ :",
                      style: const TextStyle(
                        fontSize: 13,
                        fontWeight: FontWeight.w500,
                        color: Color(0xFF2B1710),
                      )),
                  Text("฿ ${formattedPrice.format(price)}",
                      style: const TextStyle(
                        fontSize: 13,
                        fontWeight: FontWeight.w500,
                        color: Color(0xFF2B1710),
                      )),
                ],
              ),
              const SizedBox(
                height: 5,
              ),
              GetBuilder<ProvinceController>(
                builder: (provincCtrl) {
                  // Ensure address is fetched (you can call it in initState or similar)
                  provincCtrl.fetchAddressForOrder(
                      sparepartOrderCtl.listOrder[mainIndex]
                          ["shipping_address_id"],
                      mainIndex);

                  return Obx(() => Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text("ที่อยู่สำหรับจัดส่ง",
                              style: TextStyle(
                                fontSize: 13,
                                fontWeight: FontWeight.w500,
                                color: Color(0xFF2B1710),
                              )),
                          Row(
                            children: [
                              SvgPicture.asset(
                                  "assets/icon/sparePart/Location.svg"),
                              const SizedBox(width: 10),
                              SizedBox(
                                width: 130,
                                child: Text(
                                  provincCtrl.shippingAddress.isEmpty
                                      ? "กำลังโหลด..." // Show loading text if the list is empty
                                      : provincCtrl.shippingAddress[mainIndex],
                                  // Show the fetched address at the specific index
                                  overflow: TextOverflow.ellipsis,
                                  // Add ellipsis for overflow
                                  style: const TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w500,
                                    color: Color(0xFF707070),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ));
                },
              ),
              SizedBox(
                width: MediaQuery.of(context).size.width,
                height: 20,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: List.generate(
                      (MediaQuery.of(context).size.width / 8).floor(), (index) {
                    return const Text(
                      "-",
                      style: TextStyle(fontSize: 12, color: Color(0xFFBCBCBC)),
                    );
                  }),
                ),
              ),
              sparepartOrderCtl.listOrder[mainIndex]
              ["status_order"] ==
                  "AWAITING_PAYMENT"
                  ?Column(
                  children: [
                    Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text("ใช้ PMSPoint เป็นส่วนลด",
                                    style: TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                      color: Color(0xFF2B1710),
                                    )),
                                Row(
                                  children: [
                                    Image.asset(
                                      'assets/image/icon-likepoint.png',
                                      width: 20,
                                    ),
                                    const SizedBox(
                                      width: 5,
                                    ),
                                    Text(webviewCtl.balanceLikePoint.value,
                                        style: const TextStyle(
                                          fontSize: 14,
                                          fontWeight: FontWeight.w500,
                                          color: Color(0xFF2B1710),
                                        )),
                                  ],
                                )
                              ],
                            ),
                            Container(
                              width: MediaQuery.of(context).size.width * 0.4,
                              height: 45,
                              decoration: BoxDecoration(
                                color: const Color(0x0D282828),
                                borderRadius: BorderRadius.circular(10),
                              ),
                              alignment: Alignment.center,
                              //TextField
                              child: TextField(
                                controller: pmsPointCtrl[mainIndex],
                                keyboardType: TextInputType.number,
                                textAlign: TextAlign.center,
                                onChanged: (value) {
                                  setState(() {
                                    if (int.parse(value) > 20000) {
                                      pmsPointCtrl[mainIndex].text = "20000";
                                    }
                                  });
                                },
                                enabled: usePMSPoint[mainIndex] &&
                                    sparepartOrderCtl.listOrder[mainIndex]
                                    ["slip_payment"] ==
                                        null &&
                                    sparepartOrderCtl.listOrder[mainIndex]
                                    ["discount_points"] ==
                                        0,
                                decoration: const InputDecoration(
                                  hintText: "กรอกจำนวน",
                                  hintStyle: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                    color: Color(0xFF707070),
                                  ),
                                  border: InputBorder.none,
                                ),
                                style: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  color: Color(0xFF2B1710),
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(
                          height: 10,
                        ),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            SizedBox(
                              height: 20,
                              width: 20,
                              child: Checkbox(
                                  value: usePMSPoint[mainIndex],
                                  // splashRadius: 1,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(5),
                                  ),
                                  side: const BorderSide(
                                    color: Color(0xFFBCBCBC),
                                    width: 2,
                                    strokeAlign: 1.0,
                                  ),
                                  activeColor: const Color(0xFFFFB100),
                                  onChanged: (value) {
                                    setState(() {
                                      usePMSPoint[mainIndex] = value!;
                                      pmsPointCtrl[mainIndex].text = "";
                                    });
                                  }),
                            ),
                            const SizedBox(
                              width: 5,
                            ),
                            const Text(
                              "ใช้ PMSPoint สูงสุด 20,000",
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: Color(0xFF282828),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                    SizedBox(
                      width: MediaQuery.of(context).size.width,
                      height: 20,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: List.generate(
                            (MediaQuery.of(context).size.width / 8).floor(), (index) {
                          return const Text(
                            "-",
                            style: TextStyle(fontSize: 12, color: Color(0xFFBCBCBC)),
                          );
                        }),
                      ),
                    ),
                  ],
              )
                  :Container(),
              Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text("ส่วนลด PMSpoint :",
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: Color(0xFF2B1710),
                          )),
                      Text(
                          sparepartOrderCtl.listOrder[mainIndex]
                                      ["discount_points"] ==
                                  null
                              ? pmsPointCtrl[mainIndex].text
                              : sparepartOrderCtl.listOrder[mainIndex]
                                      ["discount_points"]
                                  .toString(),
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: Color(0xFF2B1710),
                          ))
                    ],
                  ),
                  // sparepartOrderCtl.listOrder[mainIndex]
                  //             ["shipping_address_id"] ==
                  //         -1
                  //     ? Container()
                  //     :
                  Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text("ค่าจัดส่ง :",
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  color: Color(0xFF2B1710),
                                )),
                            Text(
                                "${totalShipping == 0 ? "ฟรี" : "${formattedPrice.format(totalShipping)}"}",
                                style: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  color: Color(0xFF2B1710),
                                )),
                          ],
                        ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text("ยอดรวม :",
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: Color(0xFF2B1710),
                          )),
                      Text(
                          "฿ ${formattedPrice.format(totalPrice - (int.parse(pmsPointCtrl[mainIndex].text.isEmpty ? '0' : pmsPointCtrl[mainIndex].text) * double.parse(webviewCtl.exchangeRate.value)) - (int.parse(pmsPointCtrl[mainIndex].text.isEmpty ? '0' : pmsPointCtrl[mainIndex].text) * double.parse(webviewCtl.exchangeRate.value)))}",
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF895F00),
                          )),
                    ],
                  ),
                  const SizedBox(
                    height: 5,
                  ),

                  // TODO :: PAYMENT
                  sparepartOrderCtl.listOrder[mainIndex]
                              ["status_order"] ==
                          "AWAITING_PAYMENT"
                      ? Column(
                          children: [
                            Container(
                              alignment: Alignment.centerLeft,
                              child: const Text(
                                "การชำระเงิน",
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                  color: Color(0xFF2B1710),
                                ),
                              ),
                            ),
                            const SizedBox(
                              height: 5,
                            ),
                            GetBuilder<PaymentTimer>(builder: (timeCtrl) {
                              return Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Container(
                                      width: MediaQuery.of(context).size.width *
                                          0.5,
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          const Text(
                                              "บ.ประชากิจมอเตอร์เซลส์ \nธนาคารกสิกรไทย",
                                              maxLines: 2,
                                              overflow: TextOverflow.ellipsis,
                                              style: TextStyle(
                                                fontSize: 14,
                                                color: Color(0xFF707070),
                                              )),
                                          InkWell(
                                            onTap: () {
                                              // Copy the text to the clipboard
                                              Clipboard.setData(
                                                  const ClipboardData(
                                                      text: "1481082354"));
                                              // Optionally, show a message to the user confirming the copy action
                                              ScaffoldMessenger.of(context)
                                                  .showSnackBar(
                                                const SnackBar(
                                                    content: Text(
                                                        'Copied to clipboard')),
                                              );
                                            },
                                            child: Row(
                                              children: [
                                                const Text("148-1-08235-4",
                                                    style: TextStyle(
                                                      fontSize: 14,
                                                      color: Color(0xFF707070),
                                                    )),
                                                SizedBox(
                                                  width: 5,
                                                ),
                                                Icon(
                                                  Icons.copy,
                                                  color: Color(0xFF707070),
                                                  size: 14,
                                                ),
                                              ],
                                            ),
                                          )
                                        ],
                                      )),
                                  sparepartOrderCtl.listOrder[mainIndex]
                                                  ["qr_code_expire"] ==
                                              null ||
                                          (int.parse(sparepartOrderCtl
                                                                  .listOrder[
                                                              mainIndex]
                                                          ["qr_code_expire"] ??
                                                      "0") -
                                                  timeCtrl.start) <
                                              0
                                      ? InkWell(
                                          onTap: () async {
                                            if (sparepartOrderCtl
                                                        .listOrder[mainIndex]
                                                    ["slip_payment"] ==
                                                null) {
                                              // กรณี QR Code หมดอายุ
                                              timeCtrl.updateIndex(mainIndex);
                                              _openQRPopup();
                                              await timeCtrl.createQrPayment(
                                                  sparepartOrderCtl
                                                          .listOrder[mainIndex]
                                                      ["order_id"],
                                                  pmsPointCtrl[mainIndex].text,
                                                  webviewCtl.pocketID.value,
                                                  totalPrice,
                                                  (int.parse(pmsPointCtrl[
                                                                  mainIndex]
                                                              .text
                                                              .isEmpty
                                                          ? '0'
                                                          : pmsPointCtrl[
                                                                  mainIndex]
                                                              .text) *
                                                      double.parse(webviewCtl
                                                          .exchangeRate
                                                          .value)));
                                            } else {
                                              Fluttertoast.showToast(
                                                  msg:
                                                      "ท่านได้อัปโหลดสลิปเรียบร้อยแล้ว อยู่ระหว่างการตรวจสอบ",
                                                  toastLength:
                                                      Toast.LENGTH_SHORT,
                                                  gravity: ToastGravity.CENTER,
                                                  timeInSecForIosWeb: 1,
                                                  backgroundColor: Colors.red,
                                                  textColor: Colors.white,
                                                  fontSize: 16.0);
                                            }
                                          },
                                          child: Container(
                                              width: MediaQuery.of(context)
                                                      .size
                                                      .width *
                                                  0.3,
                                              height: 45,
                                              decoration: BoxDecoration(
                                                borderRadius:
                                                    BorderRadius.circular(10),
                                                gradient: const LinearGradient(
                                                  begin: Alignment(0.00, -1.00),
                                                  end: Alignment(0.00, 1.00),
                                                  colors: [
                                                    Color(0xFFE8E6E2),
                                                    Color(0xFFD9D8D5)
                                                  ],
                                                ),
                                              ),
                                              // alignment: Alignment.center,
                                              child: Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.center,
                                                children: [
                                                  SvgPicture.asset(
                                                    "assets/icon/sparePart/qr-code.svg",
                                                    width: 20,
                                                  ),
                                                  const SizedBox(
                                                    width: 5,
                                                  ),
                                                  const Text(
                                                    "คิวอาร์โค้ด",
                                                    style: TextStyle(
                                                      fontSize: 14,
                                                      fontWeight:
                                                          FontWeight.w500,
                                                      color: Color(0xFF2B1710),
                                                    ),
                                                  ),
                                                ],
                                              )),
                                        )
                                      : InkWell(
                                          onTap: () {
                                            if (sparepartOrderCtl
                                                        .listOrder[mainIndex]
                                                    ["slip_payment"] ==
                                                null) {
                                              timeCtrl.updateIndex(mainIndex);
                                              _openQRPopup();
                                            } else {
                                              Fluttertoast.showToast(
                                                  msg:
                                                      "ท่านได้อัปโหลดสลิปเรียบร้อยแล้ว อยู่ระหว่างการตรวจสอบ",
                                                  toastLength:
                                                      Toast.LENGTH_SHORT,
                                                  gravity: ToastGravity.CENTER,
                                                  timeInSecForIosWeb: 1,
                                                  backgroundColor: Colors.red,
                                                  textColor: Colors.white,
                                                  fontSize: 16.0);
                                            }
                                          },
                                          child: Container(
                                              width: MediaQuery.of(context)
                                                      .size
                                                      .width *
                                                  0.3,
                                              height: 45,
                                              decoration: BoxDecoration(
                                                borderRadius:
                                                    BorderRadius.circular(10),
                                                gradient: const LinearGradient(
                                                  begin: Alignment(0.00, -1.00),
                                                  end: Alignment(0.00, 1.00),
                                                  colors: [
                                                    Color(0xFFE8E6E2),
                                                    Color(0xFFD9D8D5)
                                                  ],
                                                ),
                                              ),
                                              // alignment: Alignment.center,
                                              child: Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.center,
                                                children: [
                                                  SvgPicture.asset(
                                                    "assets/icon/sparePart/qr-code.svg",
                                                    width: 20,
                                                  ),
                                                  const SizedBox(
                                                    width: 5,
                                                  ),
                                                  Column(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .center,
                                                    children: [
                                                      const Text(
                                                        "ชำระเงินภายใน",
                                                        style: TextStyle(
                                                          fontSize: 12,
                                                          fontWeight:
                                                              FontWeight.w500,
                                                          color:
                                                              Color(0xFF2B1710),
                                                        ),
                                                      ),
                                                      Text(
                                                        (int.parse(sparepartOrderCtl.listOrder[mainIndex]
                                                                            [
                                                                            "qr_code_expire"] ??
                                                                        0) -
                                                                    timeCtrl
                                                                        .start) <
                                                                0
                                                            ? "00:00"
                                                            : "${((int.parse(sparepartOrderCtl.listOrder[mainIndex]["qr_code_expire"] ?? 0) - timeCtrl.start) ~/ 60000).toString().padLeft(2, '0')}:${(((int.parse(sparepartOrderCtl.listOrder[mainIndex]["qr_code_expire"] ?? '0') - timeCtrl.start) % 60000) ~/ 1000).toString().padLeft(2, '0')}",
                                                        style: const TextStyle(
                                                          fontSize: 12,
                                                          fontWeight:
                                                              FontWeight.w500,
                                                          color:
                                                              Color(0xFFEB2227),
                                                        ),
                                                      ),
                                                    ],
                                                  )
                                                ],
                                              )),
                                        ),
                                ],
                              );
                            }),
                            const SizedBox(
                              height: 10,
                            ),
                            Obx(
                              () => Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Container(
                                    width:
                                        MediaQuery.of(context).size.width * 0.4,
                                    child: const Text("หลักฐานการชำระเงิน",
                                        maxLines: 2,
                                        overflow: TextOverflow.ellipsis,
                                        style: TextStyle(
                                          fontSize: 14,
                                          color: Color(0xFF707070),
                                        )),
                                  ),
                                  sparepartOrderCtl.listOrder[mainIndex]['slip_payment'] == null ?
                                  sparepartOrderCtl.slipLink.value == ''
                                      ? InkWell(
                                          onTap: () {
                                            if(timeCtrl.qrData != "" && timeCtrl.qrData == null){
                                              if (sparepartOrderCtl.listOrder[mainIndex]["slip_payment"] == null ) {
                                                _buildBottomSheetChooseImage(sparepartOrderCtl.listOrder[mainIndex]["order_id"]);
                                              }
                                              else {
                                                Fluttertoast.showToast(
                                                    msg: "ท่านได้อัปโหลดสลิปเรียบร้อยแล้ว อยู่ระหว่างการตรวจสอบ",
                                                    toastLength:
                                                    Toast.LENGTH_SHORT,
                                                    gravity: ToastGravity.CENTER,
                                                    timeInSecForIosWeb: 1,
                                                    backgroundColor: Colors.red,
                                                    textColor: Colors.white,
                                                    fontSize: 16.0);
                                              }
                                            }else{
                                              Fluttertoast.showToast(
                                                  msg: "ท่านไม่จำเป็นต้องอัพโหลดสลิป เมื่อจ่ายผ่านคิวอาร์โค้ด",
                                                  toastLength:
                                                  Toast.LENGTH_SHORT,
                                                  gravity: ToastGravity.CENTER,
                                                  timeInSecForIosWeb: 1,
                                                  backgroundColor: Colors.red,
                                                  textColor: Colors.white,
                                                  fontSize: 16.0);
                                            }
                                          },
                                          child: Container(
                                              width: MediaQuery.of(context)
                                                      .size
                                                      .width *
                                                  0.3,
                                              height: 45,
                                              decoration: BoxDecoration(
                                                borderRadius:
                                                    BorderRadius.circular(10),
                                                color: Colors.transparent,
                                                border: Border.all(
                                                  width: 1,
                                                  color:
                                                      const Color(0xFFBCBCBC),
                                                ),
                                              ),
                                              child: Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.center,
                                                children: [
                                                  SvgPicture.asset(
                                                    "assets/icon/sparePart/image-add.svg",
                                                    width: 20,
                                                    color: Colors.black,
                                                  ),
                                                  const SizedBox(
                                                    width: 5,
                                                  ),
                                                  const Text(
                                                    "อัพโหลดสลิป",
                                                    style: TextStyle(
                                                      fontSize: 14,
                                                      fontWeight:
                                                          FontWeight.w500,
                                                      color: Color(0xFF2B1710),
                                                    ),
                                                  ),
                                                ],
                                              )),
                                        )
                                      : Row(
                                          children: [
                                            InkWell(
                                              onTap: () {
                                                sparepartOrderCtl
                                                    .deleteLinkSlip();
                                              },
                                              child: Container(
                                                width: 25,
                                                height: 25,
                                                decoration: const BoxDecoration(
                                                  color: Color(0xFFFA4862),
                                                  shape: BoxShape.circle,
                                                ),
                                                alignment: Alignment.center,
                                                child: const Icon(
                                                  size: 18,
                                                  Icons.close,
                                                  color: Colors.white,
                                                ),
                                              ),
                                            ),
                                            const SizedBox(
                                              width: 5,
                                            ),
                                            Container(
                                                width: MediaQuery.of(context)
                                                        .size
                                                        .width *
                                                    0.3,
                                                height: 45,
                                                decoration: BoxDecoration(
                                                  borderRadius:
                                                      BorderRadius.circular(10),
                                                  color:
                                                      const Color(0xFFFFB100),
                                                ),
                                                child: Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.center,
                                                  children: [
                                                    const Text(
                                                      "อัพโหลดแล้ว",
                                                      style: TextStyle(
                                                        fontSize: 14,
                                                        fontWeight:
                                                            FontWeight.w500,
                                                        color:
                                                            Color(0xFF2B1710),
                                                      ),
                                                    ),
                                                    const SizedBox(
                                                      width: 5,
                                                    ),
                                                    SvgPicture.asset(
                                                      "assets/icon/sparePart/check_ring_round.svg",
                                                      width: 20,
                                                      color: Colors.white,
                                                    ),
                                                  ],
                                                )),
                                          ],
                                        )
                                      :Container(
                                      width: MediaQuery.of(context)
                                          .size
                                          .width *
                                          0.3,
                                      height: 45,
                                      decoration: BoxDecoration(
                                        borderRadius:
                                        BorderRadius.circular(10),
                                        color:
                                        const Color(0xFFFFB100),
                                      ),
                                      child: Row(
                                        mainAxisAlignment:
                                        MainAxisAlignment.center,
                                        children: [
                                          const Text(
                                            "อัพโหลดแล้ว",
                                            style: TextStyle(
                                              fontSize: 14,
                                              fontWeight:
                                              FontWeight.w500,
                                              color:
                                              Color(0xFF2B1710),
                                            ),
                                          ),
                                          const SizedBox(
                                            width: 5,
                                          ),
                                          SvgPicture.asset(
                                            "assets/icon/sparePart/check_ring_round.svg",
                                            width: 20,
                                            color: Colors.white,
                                          ),
                                        ],
                                      )),
                                ],
                              ),
                            ),
                          ],
                        )
                      : Container(),
                  // TODO :: PAYMENT

                  if (sparepartOrderCtl.listOrder[mainIndex]["status_order"] ==
                      "SHIPPED")
                    Column(
                      children: [
                        const SizedBox(
                          height: 5,
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text("หมายเลขพัสดุ",
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  color: Color(0xFF2B1710),
                                )),
                            Row(
                              children: [
                                Text(
                                  sparepartOrderCtl.listOrder[mainIndex]["tracking_shipping"] ?? "ไม่มีข้อมูล",
                                  // "TH034587F354",
                                  style: const TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                    color: Color(0xFF2B1710),
                                  ),
                                ),
                                SizedBox(
                                  width: 10,
                                ),
                                InkWell(
                                  onTap: () async {
                                    var uri = Uri.parse(
                                        "https://th.kerryexpress.com/en/track/?track=TH034587F354");
                                    await launchUrl(uri);
                                  },
                                  child: Container(
                                    width: 50,
                                    height: 20,
                                    child: Center(
                                      child: Text(
                                        "ติดตาม",
                                        style: const TextStyle(
                                          fontSize: 14,
                                          fontWeight: FontWeight.bold,
                                          color: Color(0xFF895F00),
                                        ),
                                      ),
                                    ),
                                  ),
                                )
                              ],
                            )
                          ],
                        ),
                      ],
                    ),

                  SizedBox(
                    width: MediaQuery.of(context).size.width,
                    height: 20,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: List.generate(
                          (MediaQuery.of(context).size.width / 8).floor(),
                          (index) {
                        return const Text(
                          "-",
                          style:
                              TextStyle(fontSize: 12, color: Color(0xFFBCBCBC)),
                        );
                      }),
                    ),
                  ),
                  Obx(() {
                    if (sparepartOrderCtl.slipLink.value != "") {
                      return InkWell(
                        onTap: () async {
                          print("click");
                          AppLoader.loader(context);
                          final checked = await sparepartOrderCtl.confirmOrder(
                              sparepartOrderCtl.listOrder[mainIndex]
                                  ["order_id"],
                              int.parse(pmsPointCtrl[mainIndex].text.isEmpty
                                  ? '0'
                                  : pmsPointCtrl[mainIndex].text),
                              webviewCtl.exchangeRate.value,
                              webviewCtl.pocketID.value);
                          if (checked) {
                            AppLoader.dismiss(context);
                            Get.to(() => OrderHistory());
                          } else {
                            AppLoader.dismiss(context);
                          }
                        },
                        child: Container(
                          margin: const EdgeInsets.only(top: 10),
                          color: Colors.white,
                          child: Container(
                            width: MediaQuery.of(context).size.width,
                            height: 50,
                            // margin: EdgeInsets.all(10),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(15),
                              gradient: const LinearGradient(
                                begin: Alignment(0.00, -1.00),
                                end: Alignment(0, 1),
                                colors: [Color(0xB2000000), Color(0xFF000000)],
                              ),
                            ),
                            alignment: Alignment.center,
                            child: const Text(
                              "บันทึก",
                              style: TextStyle(
                                fontSize: 16,
                                color: Color(0xFFFFB100),
                              ),
                            ),
                          ),
                        ),
                      );
                    } else {
                      return Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          InkWell(
                            onTap: () async {
                              var uri = Uri.parse("https://lin.ee/CeJ928D");
                              await launchUrl(uri);
                            },
                            child: Container(
                                width: MediaQuery.of(context).size.width * 0.4,
                                height: 45,
                                decoration: BoxDecoration(
                                  color: Colors.transparent,
                                  borderRadius: BorderRadius.circular(10),
                                  border: Border.all(
                                    width: 1,
                                    color: const Color(0xFFBCBCBC),
                                  ),
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Image.asset(
                                      'assets/icon/contact_icon.png',
                                      width: 30,
                                    ),
                                    const SizedBox(width: 8),
                                    const Text(
                                      'สอบถามเพิ่มเติม',
                                      style: TextStyle(
                                          color: Color(0xFF282828),
                                          fontSize: 14),
                                    ),
                                  ],
                                )),
                          ),
                          sparepartOrderCtl.listOrder[mainIndex]["status_order"] ==
                              "CANCELLED" || sparepartOrderCtl.listOrder[mainIndex]["status_order"] == "DELIVERED" ||
                    sparepartOrderCtl.listOrder[mainIndex]["status_order"] == "SHIPPED"
                              ? Container()
                              : InkWell(
                            onTap: () async {
                              var result = await AppAlert.showNewConfirm(
                                  context,
                                  'ยกเลิกคำสั่งซื้อ',
                                  'คุณต้องการยกเลิกคำสั่งซื้อนี้ใช่หรือไม่?',
                                  'ใช่',
                                  'ไม่');
                              if (result) {
                                AppLoader.loader(BuildContext);
                                sparepartOrderCtl.cancelOrder(sparepartOrderCtl
                                    .listOrder[mainIndex]["order_id"]);

                                setState(() {});
                                AppLoader.dismiss(BuildContext);
                              }
                            },
                            child: Container(
                              width: MediaQuery.of(context).size.width * 0.4,
                              height: 45,
                              decoration: BoxDecoration(
                                color: const Color(0x0D282828),
                                borderRadius: BorderRadius.circular(10),
                              ),
                              alignment: Alignment.center,
                              child: const Text(
                                "ยกเลิกคำสั่งซื้อ",
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  color: Color(0xFF282828),
                                ),
                              ),
                            ),
                          )
                        ],
                      );
                    }
                  }),
                ],
              ),
              const SizedBox(
                height: 10,
              ),
            ],
          ),
        ),
      );
    });
  }

  Widget buildStatusLine(String step, mainIndex) {
    TextStyle sub = const TextStyle(
      fontSize: 12,
      fontWeight: FontWeight.w400,
      color: Color(0xFF707070),
    );

    int alreadyStep = 0;

    List<String> statusDate = [];

    switch (step) {
      case "ORDER_PLACED":
        alreadyStep = 1;
        break;
      case "AWAITING_PAYMENT":
        alreadyStep = 2;
        break;
      case "PAYMENT_COMPLETED":
        alreadyStep = 3;

        break;
      case "PREPARING_ORDER":
        alreadyStep = 4;

        break;
      case "READY_TO_SHIP":
        alreadyStep = 5;

        break;
      case "SHIPPED":
        alreadyStep = 6;

        break;
      case "AWAITING_DELIVERY":
        alreadyStep = 7;

        break;
      case "DELIVERED":
        alreadyStep = 8;

        break;
      case "CANCELLED":
        alreadyStep = 0;
        break;
    }
    if (step != "CANCELLED") {
      if (sparepartOrderCtl.listOrder[mainIndex]["order_placed_date"] != null) {
        statusDate.add(formatThaiDate(
            sparepartOrderCtl.listOrder[mainIndex]["order_placed_date"]));
      } else {
        statusDate.add("-");
      }
      if (sparepartOrderCtl.listOrder[mainIndex]["awaiting_payment_date"] !=
          null) {
        statusDate.add(formatThaiDate(
            sparepartOrderCtl.listOrder[mainIndex]["awaiting_payment_date"]));
      } else {
        statusDate.add("-");
      }
      if (sparepartOrderCtl.listOrder[mainIndex]["payment_completed_date"] !=
          null) {
        statusDate.add(formatThaiDate(
            sparepartOrderCtl.listOrder[mainIndex]["payment_completed_date"]));
      } else {
        statusDate.add("-");
      }
      if (sparepartOrderCtl.listOrder[mainIndex]["preparing_order_date"] !=
          null) {
        statusDate.add(formatThaiDate(
            sparepartOrderCtl.listOrder[mainIndex]["preparing_order_date"]));
      } else {
        statusDate.add("-");
      }
      if (sparepartOrderCtl.listOrder[mainIndex]["ready_to_ship_date"] !=
          null) {
        statusDate.add(formatThaiDate(
            sparepartOrderCtl.listOrder[mainIndex]["ready_to_ship_date"]));
      } else {
        statusDate.add("-");
      }
      if (sparepartOrderCtl.listOrder[mainIndex]["shipped_date"] != null) {
        statusDate.add(formatThaiDate(
            sparepartOrderCtl.listOrder[mainIndex]["shipped_date"]));
      } else {
        statusDate.add("-");
      }
      if (sparepartOrderCtl.listOrder[mainIndex]["awaiting_delivery_date"] != null) {
        statusDate.add(formatThaiDate(
            sparepartOrderCtl.listOrder[mainIndex]["awaiting_delivery_date"]));
      } else {
        statusDate.add("-");
      }
      if (sparepartOrderCtl.listOrder[mainIndex]["delivered_date"] != null) {
        statusDate.add(formatThaiDate(
            sparepartOrderCtl.listOrder[mainIndex]["delivered_date"]));
      } else {
        statusDate.add("-");
      }
    } else {
      if (sparepartOrderCtl.listOrder[mainIndex]["cancelled_date"] != null) {
        statusDate.add(formatThaiDate(
            sparepartOrderCtl.listOrder[mainIndex]["cancelled_date"]));
      } else {
        statusDate.add("-");
      }
    }

    return step == "CANCELLED"
        ? Column(
            verticalDirection: VerticalDirection.up,
            children: [
              Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(
                          width: MediaQuery.of(context).size.width * 0.1,
                          height: MediaQuery.of(context).size.height * 0.1 + 5,
                          child: Stack(
                            children: [
                              SizedBox(
                                  width:
                                      MediaQuery.of(context).size.width * 0.1,
                                  child: Stack(
                                    alignment: Alignment.topCenter,
                                    children: [
                                      const VerticalDivider(
                                        color: Color(0xFFBCBCBC),
                                        thickness: 1,
                                      ),
                                      Container(
                                        width: 30,
                                        height: 30,
                                        decoration: const BoxDecoration(
                                          shape: BoxShape.circle,
                                          color: Color(0xFFFFB100),
                                        ),
                                        alignment: Alignment.center,
                                        child: SvgPicture.asset(
                                          stepProcess[5].icon!,
                                          width: 15,
                                        ),
                                      ),
                                    ],
                                  )),
                            ],
                          )),
                      SizedBox(
                        width: MediaQuery.of(context).size.width * 0.7,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              stepProcess[5].text!,
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                                color: Colors.black,
                              ),
                            ),
                            const SizedBox(
                              height: 5,
                            ),
                            Text(
                              stepProcess[5].sub!,
                              maxLines: 2,
                              style: const TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                                color: Color(0xFF895F00),
                              ),
                            ),
                            const SizedBox(
                              height: 5,
                            ),
                            Text(
                              "${formatThaiDate(sparepartOrderCtl.listOrder[mainIndex]["cancelled_date"])}",
                              style: sub,
                            ),
                            const SizedBox(
                              height: 5,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(
                          width: MediaQuery.of(context).size.width * 0.1,
                          height: MediaQuery.of(context).size.height * 0.1 + 5,
                          child: Stack(
                            children: [
                              SizedBox(
                                  width:
                                      MediaQuery.of(context).size.width * 0.1,
                                  child: Stack(
                                    alignment: Alignment.topCenter,
                                    children: [
                                      Container(),
                                      Container(
                                        width: 30,
                                        height: 30,
                                        decoration: BoxDecoration(
                                          shape: BoxShape.circle,
                                          color: Colors.white,
                                          border: Border.all(
                                            width: 1,
                                            color: const Color(0xFFBCBCBC),
                                          ),
                                        ),
                                        alignment: Alignment.center,
                                        child: SvgPicture.asset(
                                          stepProcess[0].icon!,
                                          width: 15,
                                        ),
                                      ),
                                    ],
                                  )),
                            ],
                          )),
                      SizedBox(
                        width: MediaQuery.of(context).size.width * 0.7,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              stepProcess[0].text!,
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                                color: Colors.black,
                              ),
                            ),
                            const SizedBox(
                              height: 5,
                            ),
                            const Text(
                              "เจ้าหน้าที่ติดต่อกลับเพื่อยืนยันคำสั่งซื้อเรียบร้อย",
                              maxLines: 2,
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                                color: Colors.black,
                              ),
                            ),
                            const SizedBox(
                              height: 5,
                            ),
                            Text(
                              sparepartOrderCtl.listOrder[mainIndex]
                                          ["order_placed_date"] !=
                                      null
                                  ? formatThaiDate(
                                      sparepartOrderCtl.listOrder[mainIndex]
                                          ["order_placed_date"])
                                  : "",
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                                color: Colors.black,
                              ),
                            ), //
                            const SizedBox(
                              height: 5,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              )
            ],
          )
        : Column(
            verticalDirection: VerticalDirection.up,
            children: List.generate(stepProcess.length, (index) {
              final stepOrder = stepProcess[index];
              if(alreadyStep >= index + 1){
                if(alreadyStep == 7 || alreadyStep == 8){
                  if(index == 4 || index == 5){
                    return Container();
                  }else{
                    return Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            SizedBox(
                                width: MediaQuery.of(context).size.width * 0.1,
                                height:
                                MediaQuery.of(context).size.height * 0.1 +
                                    5,
                                child: Stack(
                                  children: [
                                    SizedBox(
                                        width:
                                        MediaQuery.of(context).size.width *
                                            0.1,
                                        child: Stack(
                                          alignment: Alignment.topCenter,
                                          children: [
                                            index == 0
                                                ? Container()
                                                : const VerticalDivider(
                                              color: Color(0xFFBCBCBC),
                                              thickness: 1,
                                            ),
                                            Container(
                                              width: 30,
                                              height: 30,
                                              decoration: alreadyStep ==
                                                  (index + 1)
                                                  ? const BoxDecoration(
                                                shape: BoxShape.circle,
                                                color: Color(0xFFFFB100),
                                              )
                                                  : BoxDecoration(
                                                shape: BoxShape.circle,
                                                color: Colors.white,
                                                border: Border.all(
                                                  width: 1,
                                                  color: const Color(
                                                      0xFFBCBCBC),
                                                ),
                                              ),
                                              alignment: Alignment.center,
                                              child: SvgPicture.asset(
                                                stepOrder.icon!,
                                                width: 15,
                                              ),
                                            ),
                                          ],
                                        )),
                                  ],
                                )),
                            SizedBox(
                              width: MediaQuery.of(context).size.width * 0.7,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    stepOrder.text!,
                                    style: (index + 1) != alreadyStep
                                        ? sub
                                        : const TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.black,
                                    ),
                                  ),
                                  const SizedBox(
                                    height: 5,
                                  ),
                                  Text(
                                    (index == 0 && (index + 1) < alreadyStep)
                                        ? "เจ้าหน้าที่ติดต่อกลับเพื่อยืนยันคำสั่งซื้อเรียบร้อย"
                                        : stepOrder.sub!,
                                    maxLines: 2,
                                    style: (index + 1) != alreadyStep
                                        ? sub
                                        : const TextStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.w500,
                                      color: Color(0xFF895F00),
                                    ),
                                  ),
                                  const SizedBox(
                                    height: 5,
                                  ),
                                  Text(
                                    statusDate[index] == '-'
                                        ? " "
                                        : statusDate[index],
                                    style: sub,
                                  ),
                                  const SizedBox(
                                    height: 5,
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ],
                    );
                  }
                }else{
                  return Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(
                              width: MediaQuery.of(context).size.width * 0.1,
                              height:
                              MediaQuery.of(context).size.height * 0.1 +
                                  5,
                              child: Stack(
                                children: [
                                  SizedBox(
                                      width:
                                      MediaQuery.of(context).size.width *
                                          0.1,
                                      child: Stack(
                                        alignment: Alignment.topCenter,
                                        children: [
                                          index == 0
                                              ? Container()
                                              : const VerticalDivider(
                                            color: Color(0xFFBCBCBC),
                                            thickness: 1,
                                          ),
                                          Container(
                                            width: 30,
                                            height: 30,
                                            decoration: alreadyStep ==
                                                (index + 1)
                                                ? const BoxDecoration(
                                              shape: BoxShape.circle,
                                              color: Color(0xFFFFB100),
                                            )
                                                : BoxDecoration(
                                              shape: BoxShape.circle,
                                              color: Colors.white,
                                              border: Border.all(
                                                width: 1,
                                                color: const Color(
                                                    0xFFBCBCBC),
                                              ),
                                            ),
                                            alignment: Alignment.center,
                                            child: SvgPicture.asset(
                                              stepOrder.icon!,
                                              width: 15,
                                            ),
                                          ),
                                        ],
                                      )),
                                ],
                              )),
                          SizedBox(
                            width: MediaQuery.of(context).size.width * 0.7,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  stepOrder.text!,
                                  style: (index + 1) != alreadyStep
                                      ? sub
                                      : const TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.black,
                                  ),
                                ),
                                const SizedBox(
                                  height: 5,
                                ),
                                Text(
                                  (index == 0 && (index + 1) < alreadyStep)
                                      ? "เจ้าหน้าที่ติดต่อกลับเพื่อยืนยันคำสั่งซื้อเรียบร้อย"
                                      : stepOrder.sub!,
                                  maxLines: 2,
                                  style: (index + 1) != alreadyStep
                                      ? sub
                                      : const TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w500,
                                    color: Color(0xFF895F00),
                                  ),
                                ),
                                const SizedBox(
                                  height: 5,
                                ),
                                Text(
                                  statusDate[index] == '-'
                                      ? " "
                                      : statusDate[index],
                                  style: sub,
                                ),
                                const SizedBox(
                                  height: 5,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  );
                }

              }else{
                return Container();
              }

            }),
          );
  }

  void _openQRPopup() {
    showDialog(context: context, builder: (_) => const PaymentState());
  }

  Future _buildBottomSheetChooseImage(orderId) {
    // var orderID = orderId;
    return showModalBottomSheet(
        context: context,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(25.0),
          ),
        ),
        builder: (context) {
          return SizedBox(
            height: 245,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        width: 42,
                        height: 3,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(2.0),
                          color: const Color(0xAA282828),
                        ),
                      ),
                    ],
                  ),
                ),
                Column(
                  children: [
                    InkWell(
                        onTap: () async {
                          AppLoader.loader(context);
                          var link = await sparepartOrderCtl.pickImage();
                          await sparepartOrderCtl.saveLinkSlip(link);
                          AppLoader.dismiss(context);
                          Navigator.pop(context);
                        },
                        child: Container(
                          width: MediaQuery.of(context).size.width * 0.85,
                          height: 60,
                          decoration: BoxDecoration(
                            color: const Color(0x0D282828),
                            borderRadius: BorderRadius.circular(15),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              SvgPicture.asset(
                                "assets/icon/sparePart/image-add.svg",
                                width: 30,
                                color: Colors.black,
                              ),
                              const SizedBox(
                                width: 5,
                              ),
                              const Text(
                                "อัพโหลดรูปจากเครื่อง",
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  color: Color(0xFF282828),
                                ),
                                textAlign: TextAlign.left,
                              ),
                            ],
                          ),
                        )),
                    const SizedBox(
                      height: 10,
                    ),
                    InkWell(
                        onTap: () {
                          Navigator.pop(context);
                        },
                        child: Container(
                          width: MediaQuery.of(context).size.width * 0.85,
                          height: 60,
                          alignment: Alignment.center,
                          child: const Text(
                            "ยกเลิก",
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF282828),
                            ),
                            textAlign: TextAlign.left,
                          ),
                        )),
                  ],
                ),
                const SizedBox(
                  height: 10,
                ),
              ],
            ),
          );
        });
  }
}
