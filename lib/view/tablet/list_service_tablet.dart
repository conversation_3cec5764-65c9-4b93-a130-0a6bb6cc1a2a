import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/secureStorage.dart';
import 'package:mapp_prachakij_v3/component/widget.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/page_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/accessory/accessory.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/appointment/appointment.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/broken_car/broken_car.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/home_service/home_service.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/insurance/insurance.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/pmg/pmg.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/talksc/talk_SC.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/sa/sa.dart';
import 'package:mapp_prachakij_v3/view/login/login.dart';

class ListServiceTabletPage extends StatefulWidget {
  const ListServiceTabletPage({Key? key}) : super(key: key);

  @override
  State<ListServiceTabletPage> createState() => _ListServiceTabletPageState();
}

class _ListServiceTabletPageState extends State<ListServiceTabletPage> {
  final SecureStorage secureStorage = SecureStorage();
  final ScrollController scrollController = ScrollController();

  String? phoneCrashCar;
  String? phoneCallCenter;

  bool statusCrashCar = false;
  bool statusAppointment = false;
  bool statusHomeService = false;
  bool statusLicense = false;
  bool statusColors = false;
  bool statusServicePart = false;
  bool statusAccessory = false;
  bool statusTalkSale = false;
  bool statusSA = false;
  bool statusMR = false;

  final dataProfileCtl = Get.put(ProfileController());
  final pageCtl = Get.put(PageSelectController());

  static FirebaseAnalytics analytics = FirebaseAnalytics.instance;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    analytics.setCurrentScreen(screenName: "Home");
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 8, right: 8),
      child: Container(
        decoration: BoxDecoration(
          color: const Color(0xFFFFFFFF),
          borderRadius: BorderRadius.circular(15),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              spreadRadius: 0,
              blurRadius: 10,
              offset: const Offset(0, 4), // changes position of shadow
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            const SizedBox(
              height: 20,
            ),
            AppWidget.boldTextS(context, "งานบริการ", 14,
                const Color(0xFF282828), FontWeight.w500),
            const SizedBox(
              height: 24,
            ),
            Container(
              padding: const EdgeInsets.only(left: 50, right: 50),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  buildRowMenu(
                    "menu_appoint.png",
                    "จองคิว",
                    "ศูนย์บริการ",
                    "login",
                    '/appointment',
                  ),
                  const SizedBox(
                    width: 60,
                  ),
                  buildRowMenu(
                    "menu_emer.png",
                    "รถเสีย",
                    "แจ้งปัญหา",
                    "guest",
                    '/broken-car',
                  ),
                  const SizedBox(
                    width: 60,
                  ),
                  buildRowMenu(
                    "menu_home_repair.png",
                    "บริการซ่อม",
                    "ถึงบ้าน",
                    "guest",
                    '/home-service',
                  ),
                ],
              ),
            ),
            const SizedBox(
              height: 20,
            ),
            Container(
              padding: const EdgeInsets.only(left: 50, right: 50),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  buildRowMenu(
                    "menu_accessories.png",
                    "อะไหล่",
                    "ประยนต์",
                    "guest",
                    const AccessoryPage(),
                  ),
                  const SizedBox(
                    width: 60,
                  ),
                  buildRowMenu(
                    "menu_sa.png",
                    "ปรึกษางาน",
                    "ศูนย์บริการ",
                    "guest",
                    const SAPage(),
                  ),
                  const SizedBox(
                    width: 60,
                  ),
                  buildRowMenu(
                    "menu_fix.png",
                    "ศูนย์ซ่อมสี",
                    "และตัวถัง",
                    "guest",
                    const PMGPage(),
                  ),
                ],
              ),
            ),
            const SizedBox(
              height: 20,
            ),
            Container(
              padding: const EdgeInsets.only(left: 50, right: 50),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  buildRowMenu(
                    "menu_sale.png",
                    "สนใจซื้อรถ",
                    "ติดต่อเซลส์",
                    "guest",
                    const TalkSCPage(),
                  ),
                  const SizedBox(
                    width: 60,
                  ),
                  buildRowMenu(
                    "menu_license.png",
                    "ทะเบียน",
                    "ประกัน/พรบ.",
                    "guest",
                    const InsurancePage(),
                  ),
                  const SizedBox(
                    width: 60,
                  ),
                  const SizedBox(
                    width: 59,
                    height: 48,
                  )
                ],
              ),
            ),
            const SizedBox(
              height: 20,
            ),
          ],
        ),
      ),
    );
  }

  buildRowMenu(picture, tile, subtitle, showType, routeName) {
    return InkWell(
      onTap: () {
        if (showType == "guest") {
          Get.toNamed(routeName);
        } else {
          if (dataProfileCtl.token.value != null) {
            Get.toNamed(routeName);
          } else {
            AppWidget.showDialogPageSlide(context, const LoginPage());
          }
        }
      },
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
              width: 59,
              height: 48,
              decoration: const BoxDecoration(
                borderRadius: BorderRadius.all(Radius.circular(15)),
                color: Colors.white,
                border: Border.fromBorderSide(
                  BorderSide(
                    color: Color(0xFFE8E6E2),
                    width: 1,
                  ),
                ),
              ),
              padding: const EdgeInsets.all(8),
              child: Image.asset(
                "assets/image/service/$picture",
              )),
          const SizedBox(
            height: 4,
          ),
          AppWidget.boldText(
              context, tile, 12, const Color(0xFF282828), FontWeight.w600),
          AppWidget.normalText(
              context, subtitle, 12, const Color(0xFF282828), FontWeight.w400)
        ],
      ),
    );
  }
}
