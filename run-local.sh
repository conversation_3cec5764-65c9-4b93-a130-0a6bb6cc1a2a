#!/bin/bash

# 🚀 Local Development Script for Mapp Prachakij V3
# This script runs the app locally for testing before production deployment

echo "🚀 Starting Local Development Server..."

# ✅ Clean previous builds
echo "🧹 Cleaning previous builds..."
flutter clean
flutter pub get

# ✅ Check for any compilation errors
echo "🔍 Checking for compilation errors..."
flutter analyze --no-fatal-infos

# ✅ Build for web with development settings
echo "🔨 Building for local development..."
flutter build web \
  --web-renderer html \
  --dart-define=FLUTTER_WEB_USE_SKIA=false \
  --dart-define=ENVIRONMENT=development \
  --source-maps \
  --no-tree-shake-icons

if [ $? -ne 0 ]; then
    echo "❌ Build failed! Please fix compilation errors."
    exit 1
fi

# ✅ Start local server
echo "🌐 Starting local server on http://localhost:8080"
echo "📋 Press Ctrl+C to stop the server"
echo "🔍 Check browser console for debugging information"
echo "==========================================="

# Start server with proper CORS headers for local development
cd build/web
python3 -m http.server 8080 --bind 127.0.0.1

# Alternative if python3 is not available:
# php -S 127.0.0.1:8080
# or
# npx serve -s . -l 8080
