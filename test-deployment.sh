#!/bin/bash

# 🧪 Test Deployment Script for Mapp Prachakij V3
# This script performs pre-deployment checks without actually deploying

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

echo -e "${BLUE}🧪 Starting deployment readiness test...${NC}"

# Check if Flutter is installed
echo -e "${BLUE}Checking Flutter installation...${NC}"
if ! command -v flutter &> /dev/null; then
    print_error "Flutter is not installed or not in PATH"
    echo "Please install Flutter: https://flutter.dev/docs/get-started/install"
    exit 1
else
    FLUTTER_VERSION=$(flutter --version | head -n 1)
    print_status "Flutter found: $FLUTTER_VERSION"
fi

# Check if Firebase CLI is installed
echo -e "${BLUE}Checking Firebase CLI installation...${NC}"
if ! command -v firebase &> /dev/null; then
    print_error "Firebase CLI is not installed"
    echo "Install with: npm install -g firebase-tools"
    exit 1
else
    FIREBASE_VERSION=$(firebase --version)
    print_status "Firebase CLI found: $FIREBASE_VERSION"
fi

# Check Flutter doctor
echo -e "${BLUE}Running Flutter doctor...${NC}"
flutter doctor --verbose

# Check dependencies
echo -e "${BLUE}Checking dependencies...${NC}"
if [ -f "pubspec.yaml" ]; then
    print_status "pubspec.yaml found"
    flutter pub get
    print_status "Dependencies updated"
else
    print_error "pubspec.yaml not found - are you in the correct directory?"
    exit 1
fi

# Check Firebase configuration
echo -e "${BLUE}Checking Firebase configuration...${NC}"
if [ -f "firebase.json" ]; then
    print_status "firebase.json found"
else
    print_warning "firebase.json not found"
fi

if [ -f ".firebaserc" ]; then
    print_status ".firebaserc found"
    PROJECT_ID=$(cat .firebaserc | grep -o '"default": "[^"]*' | cut -d'"' -f4)
    print_info "Default project: $PROJECT_ID"
else
    print_warning ".firebaserc not found"
fi

# Test Firebase authentication
echo -e "${BLUE}Testing Firebase authentication...${NC}"
if firebase projects:list &> /dev/null; then
    print_status "Firebase authentication successful"
    firebase projects:list
else
    print_warning "Firebase authentication failed"
    echo "Run: firebase login"
fi

# Test build (without deploying)
echo -e "${BLUE}Testing build process...${NC}"
print_info "This will take a few minutes..."

# Clean first
flutter clean
print_status "Clean completed"

# Test build
if flutter build web --release --web-renderer canvaskit --dart-define=ENVIRONMENT=test; then
    print_status "Build test successful"
    
    # Check build output
    if [ -d "build/web" ]; then
        BUILD_SIZE=$(du -sh build/web | cut -f1)
        print_info "Build size: $BUILD_SIZE"
        
        # List main files
        echo -e "${BLUE}Build contents:${NC}"
        ls -la build/web/ | head -10
        
        # Check for main files
        if [ -f "build/web/index.html" ]; then
            print_status "index.html found"
        else
            print_error "index.html missing"
        fi
        
        if [ -f "build/web/main.dart.js" ]; then
            MAIN_JS_SIZE=$(du -sh build/web/main.dart.js | cut -f1)
            print_status "main.dart.js found ($MAIN_JS_SIZE)"
        else
            print_warning "main.dart.js not found"
        fi
        
    else
        print_error "Build directory not found"
        exit 1
    fi
else
    print_error "Build test failed"
    exit 1
fi

# Test gzip compression
echo -e "${BLUE}Testing gzip compression...${NC}"
if command -v gzip &> /dev/null; then
    print_status "gzip available for compression"
    
    # Test compression on a sample file
    if [ -f "build/web/main.dart.js" ]; then
        ORIGINAL_SIZE=$(stat -f%z build/web/main.dart.js 2>/dev/null || stat -c%s build/web/main.dart.js)
        gzip -k build/web/main.dart.js
        if [ -f "build/web/main.dart.js.gz" ]; then
            COMPRESSED_SIZE=$(stat -f%z build/web/main.dart.js.gz 2>/dev/null || stat -c%s build/web/main.dart.js.gz)
            COMPRESSION_RATIO=$(echo "scale=1; $COMPRESSED_SIZE * 100 / $ORIGINAL_SIZE" | bc -l 2>/dev/null || echo "N/A")
            print_status "Compression test successful (${COMPRESSION_RATIO}% of original)"
            rm build/web/main.dart.js.gz
        fi
    fi
else
    print_warning "gzip not available - compression will be skipped"
fi

# Summary
echo -e "${GREEN}"
echo "=========================================="
echo "🎉 DEPLOYMENT READINESS TEST COMPLETE!"
echo "=========================================="
echo "✅ Flutter: Ready"
echo "✅ Firebase CLI: Ready"
echo "✅ Dependencies: Ready"
echo "✅ Build: Successful"
echo "✅ Configuration: Ready"
echo "=========================================="
echo -e "${NC}"

print_info "You can now run: ./deploy.sh [environment] [renderer]"
print_info "Example: ./deploy.sh prod canvaskit"

echo -e "${YELLOW}"
echo "⚠️  Remember to:"
echo "   1. Test in staging before production"
echo "   2. Backup current deployment if needed"
echo "   3. Monitor deployment after going live"
echo -e "${NC}"
