<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # ✅ Handle Flutter routes - all non-file requests go to index.html
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} !^/favicon\.ico$
    RewriteCond %{REQUEST_URI} !^/manifest\.json$
    RewriteCond %{REQUEST_URI} !^/icons/
    RewriteRule . /index.html [L]
</IfModule>

# ✅ Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options SAMEORIGIN
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set X-XSS-Protection "1; mode=block"
    
    # ✅ Cache control for critical files
    <FilesMatch "\.(js|css|html)$">
        Header always set Cache-Control "no-cache, no-store, must-revalidate"
        Header always set Pragma "no-cache"
        Header always set Expires "0"
    </FilesMatch>
    
    # ✅ Proper MIME types
    <FilesMatch "\.js$">
        Header always set Content-Type "application/javascript; charset=utf-8"
    </FilesMatch>
    
    <FilesMatch "\.css$">
        Header always set Content-Type "text/css; charset=utf-8"
    </FilesMatch>
    
    <FilesMatch "\.html$">
        Header always set Content-Type "text/html; charset=utf-8"
    </FilesMatch>
</IfModule>

# Cache control for better performance
<IfModule mod_expires.c>
    ExpiresActive on
    
    # JavaScript files
    ExpiresByType application/javascript "access plus 5 minutes"
    ExpiresByType text/javascript "access plus 5 minutes"
    
    # CSS files
    ExpiresByType text/css "access plus 1 hour"
    
    # Images
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    
    # Fonts
    ExpiresByType font/woff "access plus 1 month"
    ExpiresByType font/woff2 "access plus 1 month"
    ExpiresByType application/font-woff "access plus 1 month"
    ExpiresByType application/font-woff2 "access plus 1 month"
    
    # Manifest and other files
    ExpiresByType application/manifest+json "access plus 1 week"
    ExpiresByType text/html "access plus 0 seconds"
</IfModule>

# Compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>
