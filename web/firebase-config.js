// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyD7XaKAZMLWd272gWrXbJl_3cpBUeo2fVM",
  authDomain: "prachakij-app.firebaseapp.com",
  projectId: "prachakij-app",
  storageBucket: "prachakij-app.appspot.com",
  messagingSenderId: "123456789012",
  appId: "1:123456789012:web:abcdef1234567890",
  measurementId: "G-ABCDEFGHIJ"
};

// Check if running on localhost
const isLocalhost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';

// Initialize Firebase when available
if (typeof firebase !== 'undefined') {
  try {
    if (firebase.apps.length === 0) {
      firebase.initializeApp(firebaseConfig);
      console.log('✅ Firebase initialized from web config');
      
      // Initialize Analytics if available
      if (firebase.analytics && !isLocalhost) {
        firebase.analytics();
        console.log('✅ Firebase Analytics initialized');
      }
    }
  } catch (error) {
    console.error('❌ Firebase web initialization error:', error);
  }
}

// Export config for use in Flutter
window.firebaseConfig = firebaseConfig;

// Log environment
console.log('🌐 Running in environment:', isLocalhost ? 'Local Development' : 'Production');
