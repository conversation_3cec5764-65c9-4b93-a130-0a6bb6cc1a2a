// ✅ Enhanced service worker with better error handling
const CACHE_NAME = 'prachakij-v1';
const urlsToCache = [
  '/',
  '/manifest.json',
  '/icons/Icon-192.png',
  '/icons/Icon-512.png'
];

// ✅ Install event
self.addEventListener('install', (event) => {
  console.log('Service Worker installing...');
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        console.log('Opened cache');
        return cache.addAll(urlsToCache);
      })
      .catch((error) => {
        console.error('Cache installation failed:', error);
      })
  );
  self.skipWaiting();
});

// ✅ Fetch event with better error handling
self.addEventListener('fetch', (event) => {
  const url = new URL(event.request.url);
  
  // Skip non-GET requests
  if (event.request.method !== 'GET') {
    return;
  }
  
  // Skip external requests
  if (url.origin !== location.origin) {
    return;
  }
  
  // Handle JS files specially
  if (url.pathname.endsWith('.js')) {
    event.respondWith(
      fetch(event.request)
        .then((response) => {
          // Check if response is actually JavaScript
          const contentType = response.headers.get('content-type');
          if (contentType && !contentType.includes('javascript') && !contentType.includes('text/plain')) {
            console.error('❌ Non-JS content served for JS file:', url.pathname);
            throw new Error('Invalid content type for JS file');
          }
          return response;
        })
        .catch((error) => {
          console.error('❌ Failed to fetch JS file:', url.pathname, error);
          // Return a basic error response
          return new Response('console.error("Failed to load script");', {
            headers: { 'content-type': 'application/javascript' }
          });
        })
    );
    return;
  }
  
  // Handle other requests
  event.respondWith(
    caches.match(event.request)
      .then((response) => {
        if (response) {
          return response;
        }
        return fetch(event.request);
      })
      .catch((error) => {
        console.error('Fetch failed:', error);
        // Return offline page or basic response
        if (event.request.destination === 'document') {
          return caches.match('/');
        }
      })
  );
});

// ✅ Activate event
self.addEventListener('activate', (event) => {
  console.log('Service Worker activating...');
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME) {
            console.log('Deleting old cache:', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
  self.clients.claim();
});