<!DOCTYPE html>
<html>
<head>
  <base href="$FLUTTER_BASE_HREF">
  
  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="Prachakij Mobile Application">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="Prachakij">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">

  <!-- ✅ Aggressive cache prevention -->
  <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate, max-age=0">
  <meta http-equiv="Pragma" content="no-cache">
  <meta http-equiv="Expires" content="0">
  
  <!-- ✅ Very relaxed CSP -->
  <meta http-equiv="Content-Security-Policy" content="default-src * 'unsafe-inline' 'unsafe-eval' data: blob:; script-src * 'unsafe-inline' 'unsafe-eval'; style-src * 'unsafe-inline'; img-src * data: blob:; connect-src *; frame-src *;">

  <link rel="apple-touch-icon" href="icons/Icon-192.png">
  <link rel="icon" type="image/png" href="favicon.png"/>
  <title>Prachakij</title>
  <link rel="manifest" href="manifest.json">

  <!-- ✅ Load error recovery first -->
  <script src="error-recovery.js?v=2.0.1" type="application/javascript"></script>
  <script src="universal-minified-fix.js?v=1.0.3" type="application/javascript"></script>

  <style>
    body {
      margin: 0;
      padding: 0;
      background: linear-gradient(135deg, #FFB200 0%, #FF8C00 100%);
      font-family: 'Prompt', -apple-system, BlinkMacSystemFont, sans-serif;
      overflow: hidden;
    }
    
    .loading {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #FFB200 0%, #FF8C00 100%);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 9999;
    }
    
    .loading-content {
      text-align: center;
      color: white;
      max-width: 300px;
      padding: 20px;
    }
    
    .loading-logo img {
      border-radius: 20px;
      box-shadow: 0 8px 32px rgba(0,0,0,0.2);
      margin-bottom: 20px;
    }
    
    .loading-text {
      font-size: 18px;
      font-weight: 500;
      margin-bottom: 20px;
    }
    
    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 4px solid rgba(255,255,255,0.3);
      border-top: 4px solid white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 20px;
    }
    
    .error-button {
      background: white;
      color: #FFB200;
      border: none;
      padding: 12px 24px;
      border-radius: 8px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      margin: 5px;
      transition: all 0.3s ease;
    }
    
    .error-button:hover {
      background: #f0f0f0;
      transform: translateY(-1px);
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</head>

<body>
  <div id="loading" class="loading">
    <div class="loading-content">
      <div class="loading-logo">
        <img src="icons/Icon-192.png" alt="Prachakij" width="80" height="80">
      </div>
      <div class="loading-text">โหลด...</div>
      <div class="loading-spinner"></div>
    </div>
  </div>

  <script>
    let loadingTimeout;
    let hasFlutterLoaded = false;
    let hasMainLoaded = false;
    let loadingHidden = false;
    let errorShown = false;

    // ✅ Check if we're in recovery mode
    const urlParams = new URLSearchParams(window.location.search);
    const isRecoveryMode = urlParams.has('force') || urlParams.has('clear') || urlParams.has('v');

    // ✅ Show error with recovery options
    function showError(msg, showButtons = true) {
      if (errorShown) return;
      errorShown = true;
      
      console.error('🚨 Showing error:', msg);
      
      const el = document.getElementById('loading');
      if (el) {
        el.innerHTML = `
          <div class="loading-content">
            <div style="font-size: 48px; margin-bottom: 20px;">⚠️</div>
            <div style="font-size: 20px; margin-bottom: 15px; font-weight: 600;">ข้อพลาด</div>
            <div style="font-size: 14px; margin-bottom: 25px; opacity: 0.9; line-height: 1.4;">${msg}</div>
            ${showButtons ? `
              <div>
                <button class="error-button" onclick="manualReload()">เฟรชหน้า</button>
                <button class="error-button" onclick="clearAndReload()">ล้างข้อมูลและเฟรช</button>
              </div>
              <div style="margin-top: 15px;">
                <button class="error-button" onclick="goHome()" style="background: rgba(255,255,255,0.2); color: white;">หน้า</button>
              </div>
            ` : ''}
          </div>
        `;
      }
    }

    // ✅ Manual reload (clean URL)
    function manualReload() {
      sessionStorage.removeItem('recoveryAttempts');
      window.location.replace(window.location.origin);
    }

    function clearAndReload() {
      try {
        localStorage.clear();
        sessionStorage.clear();
        if ('caches' in window) {
          caches.keys().then(names => names.forEach(name => caches.delete(name)));
        }
      } catch (e) {
        console.warn('Clear failed:', e);
      }
      
      setTimeout(() => {
        window.location.replace(window.location.origin);
      }, 500);
    }

    function goHome() {
      sessionStorage.removeItem('recoveryAttempts');
      window.location.replace(window.location.origin);
    }

    function updateStatus(msg) {
      const textEl = document.querySelector('.loading-text');
      if (textEl && !errorShown) {
        textEl.textContent = msg;
      }
    }

    function hideLoading() {
      if (loadingHidden || errorShown) return;
      loadingHidden = true;
      
      const loading = document.getElementById('loading');
      if (loading) {
        loading.style.opacity = '0';
        loading.style.transition = 'opacity 0.5s ease-out';
        setTimeout(() => {
          loading.style.display = 'none';
          
          // ✅ Clean URL after successful load
          if (isRecoveryMode) {
            const cleanURL = window.location.origin + window.location.pathname;
            window.history.replaceState({}, '', cleanURL);
            sessionStorage.removeItem('recoveryAttempts');
          }
        }, 500);
      }
    }

    // ✅ Enhanced script loading with better error detection
    function loadFlutterJS() {
      updateStatus('โหลด Flutter...');
      
      const script = document.createElement('script');
      script.src = `flutter.js?v=${Date.now()}`;
      script.type = 'application/javascript';
      script.defer = true;
      
      const timeout = setTimeout(() => {
        if (!hasFlutterLoaded) {
          console.error('❌ Flutter.js timeout');
          showError('ไม่สามารถโหลด Flutter ได้<br>ตรวจสอบการเชื่อมต่อเทอร์เน็ต');
        }
      }, 20000);
      
      script.onload = () => {
        clearTimeout(timeout);
        hasFlutterLoaded = true;
        console.log('✅ Flutter.js loaded successfully');
        updateStatus('Flutter โหลดเสร็จแล้ว...');
        setTimeout(loadMainScript, 500);
      };
      
      script.onerror = (e) => {
        clearTimeout(timeout);
        console.error('❌ Failed to load flutter.js:', e);
        showError('ไม่สามารถโหลด Flutter ได้<br>ลองเฟรชหน้า');
      };
      
      document.head.appendChild(script);
    }

    function loadMainScript() {
      updateStatus('เริ่มแอพ...');
      
      const script = document.createElement('script');
      script.src = `main.dart.js?v=${Date.now()}`;
      script.type = 'application/javascript';
      script.defer = true;
      
      const timeout = setTimeout(() => {
        if (!hasMainLoaded) {
          console.error('❌ main.dart.js timeout');
          showError('แอพโหลดใช้เวลานาน<br>ลองเฟรชหน้า');
        }
      }, 30000);
      
      script.onload = () => {
        clearTimeout(timeout);
        hasMainLoaded = true;
        console.log('✅ main.dart.js loaded successfully');
        updateStatus('แอพพร้อมใช้งาน...');
        
        // Auto-hide loading after successful load
        setTimeout(() => {
          if (!loadingHidden) {
            console.log('🔄 Auto-hiding loading screen');
            hideLoading();
          }
        }, 3000);
      };
      
      script.onerror = (e) => {
        clearTimeout(timeout);
        console.error('❌ Failed to load main.dart.js:', e);
        
        // Check if we got HTML instead of JS
        fetch(`main.dart.js?v=${Date.now()}`)
          .then(response => response.text())
          .then(text => {
            if (text.includes('<html>') || text.includes('<!DOCTYPE')) {
              console.error('❌ Got HTML instead of JS file');
              showError('ไฟล์แอพไม่ถูกต้อง<br>ลองล้างแคชและเฟรช');
            } else {
              showError('ไม่สามารถเริ่มแอพได้<br>ลองเฟรชหน้า');
            }
          })
          .catch(() => {
            showError('ไม่สามารถเริ่มแอพได้<br>ลองเฟรชหน้า');
          });
      };
      
      document.head.appendChild(script);
    }

    // ✅ Flutter first frame event
    window.addEventListener('flutter-first-frame', () => {
      console.log('🎉 Flutter first frame rendered');
      setTimeout(hideLoading, 1000);
    });

    // ✅ Enhanced error handlers with better detection
    window.addEventListener('error', function(e) {
      const message = e.message || e.error?.message || '';
      console.error('Global error:', message);
      
      // Detect syntax errors (HTML served as JS)
      if (message.includes('Unexpected token') || 
          message.includes('SyntaxError') ||
          message.includes('<')) {
        console.error('❌ Syntax error detected - likely HTML served as JS');
        showError('ไฟล์แอพหาย<br>ล้างแคชและเฟรช', true);
        return;
      }
      
      // Critical GetX errors
      if (message.includes('Token generation') || 
          message.includes('contextless navigation') ||
          message.includes('GetMaterialApp')) {
        console.error('❌ GetX error detected');
        showError('ข้อผิดพลาดในระบบ<br>ณารีเฟรชหน้า', true);
      }
    });

    window.addEventListener('unhandledrejection', function(e) {
      const reason = e.reason?.toString() || '';
      console.error('Unhandled rejection:', reason);
      
      // Handle promise rejections
      if (reason.includes('Token generation') || 
          reason.includes('contextless navigation') ||
          reason.includes('SyntaxError') ||
          reason.includes('Unexpected token')) {
        e.preventDefault();
        console.error('❌ Critical promise rejection');
        showError('ข้อผิดพลาดในระบบ<br>ณารีเฟรชหน้า', true);
      }
    });

    // ✅ Start loading process
    window.addEventListener('load', function() {
      console.log('🚀 Starting Flutter app loading...');
      
      if (isRecoveryMode) {
        updateStatus('แอพ...');
      }
      
      // Set overall timeout (longer)
      loadingTimeout = setTimeout(() => {
        if (!loadingHidden && !errorShown) {
          showError('แอพโหลดใช้เวลานาน<br>ลองเฟรชหน้า');
        }
      }, 45000);
      
      setTimeout(loadFlutterJS, 100);
    });

    // ✅ Cleanup on unload
    window.addEventListener('beforeunload', function() {
      if (loadingTimeout) {
        clearTimeout(loadingTimeout);
      }
    });

    // ✅ Expose global functions
    window.manualReload = manualReload;
    window.clearAndReload = clearAndReload;
    window.goHome = goHome;
  </script>
</body>
</html>
