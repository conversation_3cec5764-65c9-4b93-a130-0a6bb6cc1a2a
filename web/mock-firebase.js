// Mock Firebase for local development
console.log('🔄 Loading mock Firebase implementation');

// Create mock Firebase object
window.firebase = {
  apps: [],
  
  // Mock initializeApp
  initializeApp: function(config) {
    console.log('✅ Mock Firebase initialized with config:', config);
    this.apps.push({});
    return {};
  },
  
  // Mock messaging
  messaging: function() {
    console.log('✅ Mock Firebase Messaging initialized');
    return {
      onMessage: function(callback) {
        console.log('Mock onMessage registered');
        return function() {};
      },
      getToken: function() { 
        console.log('Mock getToken called');
        return Promise.resolve('mock-fcm-token'); 
      }
    };
  },
  
  // Mock analytics
  analytics: function() {
    console.log('✅ Mock Firebase Analytics initialized');
    return {
      logEvent: function(name, params) {
        console.log('Mock analytics event:', name, params);
      }
    };
  }
};

console.log('✅ Mock Firebase loaded successfully');