// Safari compatibility fixes
(function() {
  'use strict';
  
  // Check if Safari
  const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
  const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
  const isLocalhost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
  
  if (isSafari || isIOS) {
    console.log('Applying Safari fixes...');
    
    // Fix viewport issues
    const viewport = document.querySelector('meta[name="viewport"]');
    if (viewport) {
      viewport.setAttribute('content', 
        'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover, shrink-to-fit=no'
      );
    }
    
    // Prevent bounce scrolling
    document.body.addEventListener('touchmove', function(e) {
      if (e.target.tagName !== 'TEXTAREA' && e.target.tagName !== 'INPUT') {
        e.preventDefault();
      }
    }, { passive: false });
    
    // Handle safe area
    if (isIOS) {
      document.documentElement.style.setProperty('--safe-area-inset-top', 'env(safe-area-inset-top)');
      document.documentElement.style.setProperty('--safe-area-inset-bottom', 'env(safe-area-inset-bottom)');
    }
    
    // Fix Firebase issues on Safari + localhost
    if (isLocalhost) {
      console.log('Safari on localhost - applying Firebase fixes');
      
      // Add mock Firebase if needed
      if (typeof firebase === 'undefined') {
        console.log('Creating mock Firebase for Safari on localhost');
        window.firebase = {
          apps: [],
          initializeApp: function() {
            console.log('Safari mock Firebase initialized');
            this.apps.push({});
            return {};
          },
          messaging: function() {
            return {
              onMessage: function() {},
              getToken: function() { return Promise.resolve('safari-mock-token'); }
            };
          },
          analytics: function() {
            return { logEvent: function() {} };
          }
        };
      }
    }
  }
})();
