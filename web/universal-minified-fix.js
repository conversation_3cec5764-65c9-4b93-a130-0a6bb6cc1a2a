(function() {
  'use strict';

  console.log('Loading universal minified error fix v1.0.2...');

  // ✅ Clear all caches on load
  function clearAllCaches() {
    try {
      // Clear localStorage (keep essential data)
      if (window.localStorage) {
        const keysToKeep = ['phone', 'loginStatus', 'firstUse'];
        const allKeys = Object.keys(localStorage);
        allKeys.forEach(key => {
          if (!keysToKeep.includes(key)) {
            localStorage.removeItem(key);
          }
        });
      }
      
      // Clear sessionStorage
      if (window.sessionStorage) {
        sessionStorage.clear();
      }
      
      // Clear service worker caches
      if ('serviceWorker' in navigator && 'caches' in window) {
        caches.keys().then(names => {
          names.forEach(name => {
            caches.delete(name);
          });
        });
      }
      
      console.log('✅ Caches cleared successfully');
    } catch (e) {
      console.warn('Cache clearing failed:', e);
    }
  }

  // ✅ Enhanced URL fixing with cache busting
  function fixCurrentURL() {
    const currentURL = window.location.href;
    const hasError = currentURL.includes('minified:') || 
                    currentURL.includes('minified%3A') || 
                    currentURL.includes('error') ||
                    currentURL.includes('Token%20generation');
    
    if (hasError) {
      let cleanURL = currentURL;

      // Remove error patterns
      if (cleanURL.includes('minified:')) {
        cleanURL = cleanURL.split('minified:')[0];
      }
      if (cleanURL.includes('minified%3A')) {
        cleanURL = cleanURL.split('minified%3A')[0];
      }
      if (cleanURL.includes('error')) {
        cleanURL = cleanURL.split('error')[0];
      }
      if (cleanURL.includes('Token%20generation')) {
        cleanURL = cleanURL.split('Token%20generation')[0];
      }

      // Clean trailing characters
      cleanURL = cleanURL.replace(/[\/\?&#]+$/, '');
      
      // ✅ Add cache busting parameter
      const separator = cleanURL.includes('?') ? '&' : '?';
      cleanURL += `${separator}v=${Date.now()}&fix=1`;

      console.log('🔧 Fixing URL with cache bust:', currentURL, '->', cleanURL);

      // ✅ Clear caches before redirect
      clearAllCaches();
      
      try {
        window.location.replace(cleanURL);
      } catch (e) {
        console.error('Failed to replace URL:', e);
        // ✅ Force reload with cache clearing
        window.location.reload(true);
      }
    }
  }

  // ✅ Enhanced error recovery
  function handleError(source, message) {
    console.warn(`${source} error caught:`, message);
    
    // Clear caches immediately
    clearAllCaches();
    
    // Fix URL
    fixCurrentURL();
    
    // ✅ If URL is already clean, force reload with cache bust
    setTimeout(() => {
      const currentURL = window.location.href;
      if (!currentURL.includes('v=') && !currentURL.includes('fix=')) {
        const separator = currentURL.includes('?') ? '&' : '?';
        window.location.replace(`${currentURL}${separator}v=${Date.now()}&fix=1`);
      }
    }, 1000);
  }

  // ✅ Store original handlers
  const originalError = window.onerror;
  const originalUnhandledRejection = window.onunhandledrejection;
  const originalConsoleError = console.error;

  // ✅ Enhanced error patterns
  const errorPatterns = [
    'minified',
    'Minified', 
    'Token generation',
    'contextless navigation',
    'GetMaterialApp',
    'Get.key'
  ];

  function containsErrorPattern(message) {
    return errorPatterns.some(pattern => message.includes(pattern));
  }

  // ✅ Enhanced window error handler
  window.onerror = function(message, source, lineno, colno, error) {
    const msg = message ? message.toString() : '';
    
    if (containsErrorPattern(msg)) {
      handleError('Window', msg);
      return true; // Prevent default error handling
    }

    if (originalError) {
      return originalError.call(this, message, source, lineno, colno, error);
    }
    return false;
  };

  // ✅ Enhanced unhandled rejection handler
  window.addEventListener('unhandledrejection', function(event) {
    const reason = event.reason ? event.reason.toString() : '';

    if (containsErrorPattern(reason)) {
      handleError('Promise', reason);
      event.preventDefault();
      return;
    }

    if (originalUnhandledRejection) {
      originalUnhandledRejection.call(this, event);
    }
  });

  // ✅ Enhanced console error handler
  console.error = function(...args) {
    const message = args.join(' ');

    if (containsErrorPattern(message)) {
      handleError('Console', message);
      return;
    }

    return originalConsoleError.apply(this, args);
  };

  // ✅ Prevent minified URL changes
  function preventMinifiedURL(url) {
    if (url && (url.includes('minified:') || url.includes('minified%3A') || url.includes('Token%20generation'))) {
      console.warn('🚫 Blocked problematic URL change:', url);
      return true;
    }
    return false;
  }

  // ✅ Override history methods
  const originalReplaceState = history.replaceState;
  const originalPushState = history.pushState;

  history.replaceState = function(state, title, url) {
    if (preventMinifiedURL(url)) return;
    return originalReplaceState.call(this, state, title, url);
  };

  history.pushState = function(state, title, url) {
    if (preventMinifiedURL(url)) return;
    return originalPushState.call(this, state, title, url);
  };

  // ✅ Enhanced URL monitoring
  let lastURL = window.location.href;
  function checkURLChange() {
    const currentURL = window.location.href;
    if (currentURL !== lastURL) {
      lastURL = currentURL;

      if (containsErrorPattern(currentURL) || 
          currentURL.includes('minified:') || 
          currentURL.includes('minified%3A')) {
        handleError('URL Change', currentURL);
      }
    }
  }

  // ✅ Monitor URL changes more frequently
  setInterval(checkURLChange, 500);

  // ✅ Enhanced visibility change handler
  document.addEventListener('visibilitychange', function() {
    if (!document.hidden) {
      setTimeout(() => {
        checkURLChange();
        fixCurrentURL();
      }, 100);
    }
  });

  // ✅ Enhanced navigation handlers
  window.addEventListener('popstate', function(event) {
    setTimeout(() => {
      checkURLChange();
      fixCurrentURL();
    }, 100);
  });

  window.addEventListener('hashchange', function(event) {
    setTimeout(() => {
      checkURLChange();
      fixCurrentURL();
    }, 100);
  });

  // ✅ Enhanced fetch override
  if (window.fetch) {
    const originalFetch = window.fetch;
    window.fetch = function(url, options) {
      return originalFetch(url, options).catch(error => {
        if (error.message && containsErrorPattern(error.message)) {
          console.warn('🌐 Fetch error caught:', error.message);
          handleError('Fetch', error.message);
          throw new Error('Network error occurred');
        }
        throw error;
      });
    };
  }

  // ✅ Enhanced page load handlers
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      clearAllCaches();
      fixCurrentURL();
    });
  } else {
    clearAllCaches();
    fixCurrentURL();
  }

  // ✅ Enhanced beforeunload handler
  window.addEventListener('beforeunload', function() {
    clearAllCaches();
  });

  // ✅ Periodic health check
  setInterval(() => {
    const currentURL = window.location.href;
    if (containsErrorPattern(currentURL)) {
      console.log('🔍 Periodic check found error URL, fixing...');
      handleError('Periodic Check', currentURL);
    }
  }, 5000);

  console.log('✅ Universal minified error fix with enhanced cache clearing loaded successfully');

})();
